package cn.jwis.platform.space.ecad.remote.feign;

import cn.jwis.framework.base.feign.FeignConfig;
import cn.jwis.framework.base.response.Result;
import cn.jwis.platform.space.ecad.remote.PdmUserAuthInfo;
import cn.jwis.platform.space.ecad.remote.PdmUserInfo;
import cn.jwis.platform.space.ecad.remote.PdmUserQueryParams;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * PDM用户服务Feign接口
 */
@FeignClient(name = "pdmUserRemoteImplFeign", configuration = FeignConfig.class, url = "${pdm.user.service.gateway.url}")
public interface PdmUserRemoteImplFeign {

    /**
     * 根据用户账号查询用户信息
     */
    @GetMapping("/getUserByAccount")
    Result<PdmUserInfo> getUserByAccount(@RequestParam("account") String account);

    /**
     * 根据用户OID查询用户信息
     */
    @GetMapping("/getUserByOid")
    Result<PdmUserInfo> getUserByOid(@RequestParam("userOid") String userOid);

    /**
     * 获取用户认证信息(包含token)
     */
    @PostMapping("/getUserAuthInfo")
    Result<PdmUserAuthInfo> getUserAuthInfo(@RequestBody PdmUserQueryParams params);

    /**
     * 验证用户token有效性
     */
    @GetMapping("/auth/validateToken")
    Result<Boolean> validateToken(@RequestParam("accessToken") String accessToken);

    /**
     * 刷新用户token
     */
    @PostMapping("/auth/refreshToken")
    Result<PdmUserAuthInfo> refreshToken(@RequestParam("refreshToken") String refreshToken);

    /**
     * 用户登录获取token
     */
    @PostMapping("/auth/login")
    Result<PdmUserAuthInfo> login(@RequestParam("account") String account, 
                                  @RequestParam("password") String password);

    /**
     * 用户登出
     */
    @PostMapping("/auth/logout")
    Result<Void> logout(@RequestParam("accessToken") String accessToken);
}
