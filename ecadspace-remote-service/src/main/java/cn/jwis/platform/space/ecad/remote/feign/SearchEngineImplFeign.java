package cn.jwis.platform.space.ecad.remote.feign;

import cn.jwis.framework.base.feign.FeignConfig;
import cn.jwis.framework.base.response.Result;
import com.alibaba.fastjson.JSONObject;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(name = "searchEngineImplFeign", configuration = FeignConfig.class, url = "${search-engine.service.gateway.url}")
public interface SearchEngineImplFeign {

    @PostMapping("/search/globalSearch")
    Result<JSONObject> globalSearch(@RequestBody JSONObject jsonObject);
}
