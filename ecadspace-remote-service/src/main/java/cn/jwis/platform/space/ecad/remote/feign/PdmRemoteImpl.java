package cn.jwis.platform.space.ecad.remote.feign;

import cn.jwis.framework.base.bean.util.BeanUtil;
import cn.jwis.framework.base.domain.able.info.ModelInfo;
import cn.jwis.framework.base.exception.JWIServiceException;
import cn.jwis.framework.base.response.Result;
import cn.jwis.framework.base.util.StringUtil;
import cn.jwis.platform.space.ecad.entity.File;
import cn.jwis.platform.space.ecad.remote.*;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Component
@RequiredArgsConstructor
public class PdmRemoteImpl implements PdmRemote {

    private final PdmCadRemoteImplFeign cadRemoteImplFeign;
    private final PdmContainerImplFeign pdmContainerImplFeign;
    private final PdmFoundationImplFeign pdmFoundationImplFeign;
    private final SearchEngineImplFeign searchEngineImplFeign;
    private final PdmUserRemoteImplFeign userRemoteImplFeign;

    @Override
    public List<PdmCatalogNode> searchTree(String containerOid, String containerModel) {
        Result<List<PdmCatalogNode>> result = pdmContainerImplFeign.searchTree(containerOid, containerModel);
        if (result.getCode() != 0) {
            throw new JWIServiceException("pdmContainerImplFeign: " + result.getMsg());
        }
        return result.getResult();
    }

    @Override
    public List<PdmCADStruct> searchAllECADNodeByContainerOid(String containerOid) {
        PdmCADQueryParams params = new PdmCADQueryParams();
        params.setFromOid(containerOid);
        params.setFromType("Folder");
        params.setSearchKey("");
        params.setIndex(1);
        params.setSize(999999999);
        params.setSubTypes(Lists.newArrayList("ECADIteration"));
        Result<JSONObject> result = pdmContainerImplFeign.fuzzySubPage(params);

        if (result.getCode() != 0) {
            throw new JWIServiceException("pdmContainerImplFeign: " + result.getMsg());
        }

        return result.getResult().getJSONArray("rows").stream().map(r -> BeanUtil.clone(r, PdmCADStruct.class)).collect(Collectors.toList());
    }

    @Override
    public PdmECADIteration searchECADIterationByOid(String oid) {
        Result<PdmECADIteration> result = cadRemoteImplFeign.findByOid(oid);
        if (result.getCode() != 0) {
            throw new JWIServiceException("cadRemoteImplFeign: " + result.getMsg());
        }

        return result.getResult();
    }

    @Override
    public List<PdmCADStruct> searchECADNodeByAssemblyDocumentOids(List<String> assemblyDocumentOids) {
        return null;
    }

    @Override
    public List<PdmECADIteration> buildNodes(List<JSONObject> jsonObjects) {
        System.out.println("buildNodes： " + JSONArray.toJSONString(jsonObjects));
        Result<List<PdmECADIteration>> result = cadRemoteImplFeign.buildNodes(jsonObjects);
        if (result.getCode() != 0) {
            throw new JWIServiceException("cadRemoteImplFeign: " + result.getMsg());
        }

        System.out.println("writeBackPdmData: " + JSONArray.toJSONString(result.getResult()));
        return result.getResult();
    }

    @Override
    public void buildRels(List<ECADStructureDTO> structList) {
        System.out.println("buildRels： " + JSONArray.toJSONString(structList));

        Result result = cadRemoteImplFeign.buildRels(structList);
        if (result.getCode() != 0) {
            throw new JWIServiceException("cadRemoteImplFeign: " + result.getMsg());
        }
    }

    @Override
    public void fileUploadCallback(List<JSONObject> jsonObjects) {
        System.out.println("fileUploadCallback： " + JSONArray.toJSONString(jsonObjects));

        Result result = cadRemoteImplFeign.fileUploadCallback(jsonObjects);
        if (result.getCode() != 0) {
            throw new JWIServiceException("cadRemoteImplFeign: " + result.getMsg());
        }
    }

    @Override
    public PdmTreeStructure findSecBom(String rootOid) {
        Result<PdmTreeStructure> result = cadRemoteImplFeign.findSecBom(rootOid);
        if (result.getCode() != 0) {
            throw new JWIServiceException("cadRemoteImplFeign: " + result.getMsg());
        }

        return result.getResult();
    }

    @Override
    public PdmTreeStructure batchGetCadDocByOid(String oid) {
        Result<PdmTreeStructure> result = cadRemoteImplFeign.batchGetCadDocByOid(oid);
        if (result.getCode() != 0) {
            throw new JWIServiceException("cadRemoteImplFeign: " + result.getMsg());
        }

        return result.getResult();
    }

    @Override
    public List<PdmECADFile> getFileList(List<String> allPmdECADIntegrationOids) {
        List<GetFileListParams> params = allPmdECADIntegrationOids.stream().map(oid -> new GetFileListParams(oid, true, "")).collect(Collectors.toList());
        List<GetFileListParams> params2 = allPmdECADIntegrationOids.stream().map(oid -> new GetFileListParams(oid, false, "")).collect(Collectors.toList());

        Result<List<PdmECADFile>> result = cadRemoteImplFeign.getFileList(params);
        Result<List<PdmECADFile>> result2 = cadRemoteImplFeign.getFileList(params2);
        if (result.getCode() != 0) {
            throw new JWIServiceException("cadRemoteImplFeign: " + result.getMsg());
        }
        List<PdmECADFile> list = result.getResult();
        if (list == null) {
            list = new ArrayList<>();
        }

        if (result2.getResult() != null && !result2.getResult().isEmpty()) {
            List<PdmECADFile> list2 = result2.getResult().stream()
                    .filter(p -> p.getFileType().equals("thumbnail") || p.getFileType().equals("3DX_High")).collect(Collectors.toList());
            list.addAll(list2);
        }
        return list;
    }

    @Override
    public List<PdmCatalog> findAllFolderByOid(String catalogOid) {
        Result<JSONObject> result = cadRemoteImplFeign.findAllFolderByOids(Lists.newArrayList(catalogOid));
        if (result.getCode() != 0) {
            throw new JWIServiceException("cadRemoteImplFeign: " + result.getMsg());
        }

        JSONObject jsonObject = result.getResult();
        if (jsonObject == null) {
            throw new JWIServiceException("查无此文件夹: " + catalogOid);
        }
        String ids = jsonObject.getString(catalogOid + "-id");
        String paths = jsonObject.getString(catalogOid + "-path");

        if (StringUtil.isBlank(ids)) {
            throw new JWIServiceException("查无此文件夹: " + catalogOid);
        }

        String[] idSplit = ids.split("/");
        String[] pathSplit = paths.split("/");
        Result<PdmCatalog> folderResult = pdmFoundationImplFeign.findByOid(idSplit[0], "Folder");
        if (folderResult.getCode() != 0) {
            throw new JWIServiceException("cadRemoteImplFeign: " + folderResult.getMsg());
        }

        String containerOid = folderResult.getResult().getCatalogOid();
        String containerModel = folderResult.getResult().getCatalogType();

        List<PdmCatalog> list = new ArrayList<>();
        for (int i = 0; i < idSplit.length; i++) {
            PdmCatalog pdmCatalog = new PdmCatalog();
            pdmCatalog.setOid(idSplit[i]);
            pdmCatalog.setName(pathSplit[i]);
            pdmCatalog.setContainerOid(containerOid);
            pdmCatalog.setContainerModel(containerModel);
            list.add(pdmCatalog);
        }

        return list;
    }

    @Override
    public JSONObject checkout(ModelInfo modelInfo) {
        Result<JSONObject> result = cadRemoteImplFeign.checkout(modelInfo);
        if (result.getCode() != 0) {
            throw new JWIServiceException("cadRemoteImplFeign: " + result.getMsg());
        }

        return result.getResult();
    }

    @Override
    public void cancelCheckOut(ModelInfo modelInfo) {
        Result<JSONObject> result = cadRemoteImplFeign.cancelCheckOut(modelInfo);
        if (result.getCode() != 0) {
            throw new JWIServiceException("cadRemoteImplFeign: " + result.getMsg());
        }
    }

    @Override
    public JSONObject findNextVersionNode(ECADParam param) {
        Result<List<JSONObject>> result = cadRemoteImplFeign.findNextVersionNode(Lists.newArrayList(param));
        if (result.getCode() != 0) {
            throw new JWIServiceException("cadRemoteImplFeign: " + result.getMsg());
        }

        if (result.getResult() != null && !result.getResult().isEmpty()) {
            return result.getResult().get(0);
        }
        return new JSONObject();
    }

    @Override
    public JSONObject globalSearch(JSONObject jsonParams) {
        Result<JSONObject> result = searchEngineImplFeign.globalSearch(jsonParams);
        if (result.getCode() != 0) {
            throw new JWIServiceException("searchEngineImplFeign: " + result.getMsg());
        }
        return result.getResult();
    }

    @Override
    public PdmECADIteration ecadCheckin(ECADIntegrationDTO integrationDTO) {
        System.out.println("integrationDTO: " + JSONObject.toJSONString(integrationDTO));
        Result<PdmECADIteration> result = cadRemoteImplFeign.integrationCheckIn(integrationDTO);
        if (result.getCode() != 0) {
            throw new JWIServiceException("cadRemoteImplFeign: " + result.getMsg());
        }

        return result.getResult();
    }

    @Override
    public List<PdmContainer> queryAllPdmContainer() {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("searchKey", "");
        jsonObject.put("index", 1);
        jsonObject.put("size", 999999999);

        Result<JSONObject> productContainers = pdmContainerImplFeign.queryAllProductContainer(jsonObject);
        Result<JSONObject> resourceContainers = pdmContainerImplFeign.queryAllResourceContainer(jsonObject);

        JSONArray rows = productContainers.getResult().getJSONArray("rows");
        rows.addAll(resourceContainers.getResult().getJSONArray("rows"));
        return rows.toJavaList(PdmContainer.class);
    }

    @Override
    public List<PdmPCBFile> getEcadFileList(List<String> collect) {
        Result<List<JSONObject>> result = cadRemoteImplFeign.findByOids(collect);

        List<PdmPCBFile> list = new ArrayList<>();

        for (JSONObject jsonObject : result.getResult()) {
            PdmPCBFile pdmPCBFile = new PdmPCBFile();
            pdmPCBFile.setNodeName(jsonObject.getString("name"));
            try {
                pdmPCBFile.setPrimaryFiles(jsonObject.getJSONArray("primaryFile").toJavaList(File.class));
            } catch (Exception e) {
            }

            try {
                pdmPCBFile.setSecondaryFiles(jsonObject.getJSONArray("secondaryFile").toJavaList(File.class));
            } catch (Exception e) {
            }

            try {
                pdmPCBFile.setNetList(jsonObject.getJSONObject("extensionContent").getJSONArray("netList").toJavaList(File.class));
            } catch (Exception e) {
            }

            try {
                pdmPCBFile.setBomDataOid(jsonObject.getJSONObject("extensionContent").getString("bomDataOid"));
            } catch (Exception e) {
            }
            list.add(pdmPCBFile);
        }
        return list;
    }

    @Override
    public Object initOrUpdateLibrary(String path) {
        Result<Object> result = cadRemoteImplFeign.initOrUpdateLibrary(path);
        if (result.getCode() != 0) {
            throw new JWIServiceException("cadRemoteImplFeign-initOrUpdateLibrary: " + result.getMsg());
        }
        return result.getResult();
    }

    @Override
    public Object searchCatalogTree() {
        Result<Object> result = cadRemoteImplFeign.searchCatalogTree();
        if (result.getCode() != 0) {
            throw new JWIServiceException("cadRemoteImplFeign-searchCatalogTree: " + result.getMsg());
        }
        return result.getResult();
    }

    @Override
    public Object searchSchematic() {
        Result<Object> result = cadRemoteImplFeign.searchSchematic();
        if (result.getCode() != 0) {
            throw new JWIServiceException("cadRemoteImplFeign-searchSchematic: " + result.getMsg());
        }
        return result.getResult();
    }

    @Override
    public List<PdmCADInfo> getCadInfoByNumbers(List<String> numbers, boolean pathFlag, boolean cadFileFlag) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("numbers", numbers);
        jsonObject.put("pathFlag", pathFlag);
        jsonObject.put("cadFileFlag", cadFileFlag);

        long l = System.currentTimeMillis();
        Result<List<PdmCADInfo>> result = cadRemoteImplFeign.getCadInfoByNumbers(numbers);
        if (result.getCode() != 0) {
            throw new JWIServiceException("cadRemoteImplFeign: " + result.getMsg());
        }
        System.out.println("getCadInfoByNumbers: " + JSONObject.toJSONString(result));
        System.out.println("getCadInfoByNumbers耗时: " + (System.currentTimeMillis() - l));
        if (result.getResult() != null && !result.getResult().isEmpty()) {
            return result.getResult();
        }
        return new ArrayList<>();
    }

    @Override
    public Object syncToolData(Object object) {
        Result<Object> result = cadRemoteImplFeign.syncToolData(object);
        if (result.getCode() != 0) {
            throw new JWIServiceException("cadRemote.syncToolData: " + result.getMsg());
        }
        return result.getResult();
    }

    @Override
    public PdmUserInfo getUserByAccount(String account) {
        Result<PdmUserInfo> result = userRemoteImplFeign.getUserByAccount(account);
        if (result.getCode() != 0) {
            throw new JWIServiceException("userRemoteImplFeign.getUserByAccount: " + result.getMsg());
        }
        return result.getResult();
    }

    @Override
    public PdmUserInfo getUserByOid(String userOid) {
        Result<PdmUserInfo> result = userRemoteImplFeign.getUserByOid(userOid);
        if (result.getCode() != 0) {
            throw new JWIServiceException("userRemoteImplFeign.getUserByOid: " + result.getMsg());
        }
        return result.getResult();
    }

    @Override
    public PdmUserAuthInfo getUserAuthInfo(PdmUserQueryParams params) {
        Result<PdmUserAuthInfo> result = userRemoteImplFeign.getUserAuthInfo(params);
        if (result.getCode() != 0) {
            throw new JWIServiceException("userRemoteImplFeign.getUserAuthInfo: " + result.getMsg());
        }
        return result.getResult();
    }

    @Override
    public Boolean validateToken(String accessToken) {
        Result<Boolean> result = userRemoteImplFeign.validateToken(accessToken);
        if (result.getCode() != 0) {
            throw new JWIServiceException("userRemoteImplFeign.validateToken: " + result.getMsg());
        }
        return result.getResult();
    }

    @Override
    public PdmUserAuthInfo refreshToken(String refreshToken) {
        Result<PdmUserAuthInfo> result = userRemoteImplFeign.refreshToken(refreshToken);
        if (result.getCode() != 0) {
            throw new JWIServiceException("userRemoteImplFeign.refreshToken: " + result.getMsg());
        }
        return result.getResult();
    }
}
