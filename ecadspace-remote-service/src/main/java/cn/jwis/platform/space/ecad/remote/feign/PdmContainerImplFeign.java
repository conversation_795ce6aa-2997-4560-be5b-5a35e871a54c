package cn.jwis.platform.space.ecad.remote.feign;

import cn.jwis.framework.base.feign.FeignConfig;
import cn.jwis.framework.base.response.Result;
import cn.jwis.platform.space.ecad.remote.PdmCatalogNode;
import com.alibaba.fastjson.JSONObject;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(name = "pdmContainerImplFeign", configuration = FeignConfig.class, url = "${pdm.container.service.gateway.url}")
public interface PdmContainerImplFeign {

    @GetMapping("/folder/searchTree")
    Result<List<PdmCatalogNode>> searchTree(@RequestParam String containerOid, @RequestParam String containerModel);

    @PostMapping("/folder/fuzzySubPage")
    Result<JSONObject> fuzzySubPage(@RequestBody PdmCADQueryParams params);

    @PostMapping("/container/product/search")
    Result<JSONObject> queryAllProductContainer(@RequestBody JSONObject jsonObject);

    @PostMapping("/container/resource/search")
    Result<JSONObject> queryAllResourceContainer(@RequestBody JSONObject jsonObject);
}
