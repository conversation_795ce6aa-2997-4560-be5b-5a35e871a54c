package cn.jwis.platform.space.ecad.remote.feign;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

// import javax.validation.constraints.NotBlank;
import java.util.List;

@Data
public class PdmCADQueryParams {

    @ApiModelProperty("目录/容器类型")
    private String fromType;

    // @NotBlank
    @ApiModelProperty("目录/容器oid")
    private String fromOid;

    @ApiModelProperty("需要查询的子类型")
    private List<String> subTypes;

    @ApiModelProperty("搜索词")
    private String searchKey;

    private int index = 1;
    private int size = 20;

}
