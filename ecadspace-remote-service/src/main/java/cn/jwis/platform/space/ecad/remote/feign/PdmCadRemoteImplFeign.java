package cn.jwis.platform.space.ecad.remote.feign;

import cn.jwis.framework.base.domain.able.info.ModelInfo;
import cn.jwis.framework.base.feign.FeignConfig;
import cn.jwis.framework.base.response.Result;
import cn.jwis.platform.space.ecad.remote.*;
import com.alibaba.fastjson.JSONObject;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.ArrayList;
import java.util.List;

@FeignClient(name = "pdmCadRemoteImplFeign", configuration = FeignConfig.class, url = "${pdm.cad.service.gateway.url}")
public interface PdmCadRemoteImplFeign {

    @PostMapping("/mcad/buildNodes")
    Result<List<PdmECADIteration>> buildNodes(@RequestBody List<JSONObject> jsonObjects);

    @PostMapping("/mcad/buildRels")
    Result buildRels(@RequestBody List<ECADStructureDTO> structList);

    @GetMapping("/mcad/useTree/findSecBom")
    Result<PdmTreeStructure> findSecBom(@RequestParam String rootOid);

    @GetMapping("/mcad/batchGetCadDocByOid")
    Result<PdmTreeStructure> batchGetCadDocByOid(@RequestParam String oid);

    @PostMapping("/mcad/getFileList")
    Result<List<PdmECADFile>> getFileList(@RequestBody List<GetFileListParams> allPmdECADIntegrationOids);

    @PostMapping("/mcad/fileUploadCallback")
    Result fileUploadCallback(@RequestBody List<JSONObject> jsonObjects);

    @PostMapping("/mcad/findAllFolderByOids")
    Result<JSONObject> findAllFolderByOids(@RequestBody ArrayList<String> newArrayList);

    @PostMapping("/mcad/checkOut")
    Result<JSONObject> checkout(@RequestBody ModelInfo modelInfo);

    @PostMapping("/mcad/cancelCheckOut")
    Result<JSONObject> cancelCheckOut(@RequestBody ModelInfo modelInfo);

    @PostMapping("/mcad/findNextVersionNode")
    Result<List<JSONObject>> findNextVersionNode(@RequestBody List<ECADParam> param);

    @GetMapping("/mcad/findByOid")
    Result<PdmECADIteration> findByOid(@RequestParam String oid);

    @PostMapping("/ecad/integration/checkIn")
    Result<PdmECADIteration> integrationCheckIn(@RequestBody ECADIntegrationDTO integrationDTO);

    @PostMapping("/ecad/findByOids")
    Result<List<JSONObject>> findByOids(List<String> collect);

    @GetMapping("/ecad/integration/initOrUpdateLibrary")
    Result<Object> initOrUpdateLibrary(@RequestParam("path") String path);

    @GetMapping("/ecad/integration/searchCatalogTree")
    Result<Object> searchCatalogTree();

    @GetMapping("/ecad/integration/searchSchematic")
    Result<Object> searchSchematic();

    @PostMapping("/ecad/findByNumber")
    Result<List<PdmCADInfo>> getCadInfoByNumbers(@RequestBody List<String> numbers);

    @PostMapping("/eda/syncToolData")
    Result<Object> syncToolData(@RequestBody Object object);
}
