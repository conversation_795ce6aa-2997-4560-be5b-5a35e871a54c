package cn.jwis.platform.space.ecad.remote.feign;

import cn.jwis.framework.base.feign.FeignConfig;
import cn.jwis.framework.base.response.Result;
import cn.jwis.platform.space.ecad.remote.PdmCatalog;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name = "pdmFoundationImplFeign", configuration = FeignConfig.class, url = "${pdm.foundation.service.gateway.url}")
public interface PdmFoundationImplFeign {

    @GetMapping("/instance/findByOid2")
    Result<PdmCatalog> findByOid(@RequestParam("oid") String oid, @RequestParam("type") String type);
}
