package cn.jwis.platform.space.ecad.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * ECAD迭代实体类
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ECADIteration {
    
    private String oid;
    private String name;
    private String number;
    private String pnumber;
    private String userOid;
    private String version;
    private boolean latest;
    private long iteratedVersion;
    private String displayVersion;
    
    // ECAD相关属性
    private String projectName;
    private String ecadNumber;
    private String ecadName;
    private String modelType;
    private String productPath;
    private String localPathOid;
    private String visiableOid;
    private String visiableName;
    private String bomDataOid;
    private List<File> netList;
    private String schematicNumber;
    private String ecadTrueNumber;
    private String ecadModel;
    private String productInfoPathOid;
    private String toolType;
    private String toolVersion;
    private String packagePathOid;
    private String packagePathName;
    
    // 锁相关属性
    private String lockNote;
    private String lockOwnerAccount;
    private String lockOwnerOid;
    private Long lockedTime;
    private String lockSourceOid;
    
    // 生命周期相关属性
    private String lifecycleOid;
    private String lifecycleStatus;
    
    // 容器相关属性
    private String containerOid;
    private String containerType;
    private String containerModel;
    private String spaceContainerOid;
    
    // PDM相关属性
    private String pdmVersion;
    private String pdmLatestVersion;
    private String pdmType;
    private List<String> pdmCatalogFullPaths;
    private String sourceOid;
    
    // 检入状态
    private boolean checkin;
    
    // 空间生命周期
    private String spaceLifecycleCode;
    private String spaceLifecycleStatus;
    
    // 模型定义
    private String modelDefinition;
    
    // 透明度和颜色
    private String opacity;
    private String rgb;
}
