package cn.jwis.platform.space.ecad.remote;

import cn.jwis.platform.space.ecad.entity.File;
import com.alibaba.fastjson.JSONObject;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ECADIntegrationDTO {

    private String projectName;  // 产品库名字
    private String ecadNumber;   // 存就行了
    private String ecadName;     // 节点名字，也是主文件的名字
    private String modelType;    // 存就行了
    private String productPath;  // 存就行了
    private String localPathOid; // 主文件oid
    private String visiableOid;  // 轻量化文件oid
    private String bomDataOid;   // bom文件oid
    private List<File> netList;  // 网表

    private String schematicNumber; // 原理图oid

    private boolean checkin;

    private String ecadTrueNumber;
    private String ecadModel;

    private String productInfoPathOid;
    private String toolType;
    private String toolVersion;

    private String packagePathOid;

    private List<JSONObject> steelMeshList;
}


