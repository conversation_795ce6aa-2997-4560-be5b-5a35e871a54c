package cn.jwis.platform.space.ecad.remote;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * PDM用户查询参数
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PdmUserQueryParams {
    
    @ApiModelProperty("用户账号")
    private String account;
    
    @ApiModelProperty("用户OID")
    private String userOid;
    
    @ApiModelProperty("租户OID")
    private String tenantOid;
    
    @ApiModelProperty("是否包含认证信息")
    private Boolean includeAuthInfo = false;
    
    @ApiModelProperty("是否包含角色信息")
    private Boolean includeRoles = false;
}
