package cn.jwis.platform.space.ecad.remote;

import cn.jwis.framework.base.domain.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class PdmCatalogNode extends BaseEntity {

    @ApiModelProperty("名称")
    private String name;

    @ApiModelProperty("描述")
    private String description;

    @ApiModelProperty("目录 oid, 这个是他爸 他爹 他父亲 他老子的oid")
    private String catalogOid;

    @ApiModelProperty("目录 类型")
    private String catalogType;

    @ApiModelProperty("容器 oid")
    private String containerOid;

    @ApiModelProperty("容器类型 ")
    private String containerType;

    private List<PdmCatalogNode> children;
}
