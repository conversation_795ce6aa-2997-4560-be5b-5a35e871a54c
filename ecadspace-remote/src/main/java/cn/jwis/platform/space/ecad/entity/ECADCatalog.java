package cn.jwis.platform.space.ecad.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * ECAD目录实体类
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ECADCatalog {
    
    private String oid;
    private String name;
    private String userOid;
    private boolean downloaded;
    private boolean top;
    private String containerOid;
    private String containerType;
    private String containerModel;
    private String spaceContainerOid;
}
