package cn.jwis.platform.space.ecad.remote;


import cn.jwis.framework.base.domain.able.info.ModelInfo;
import com.alibaba.fastjson.JSONObject;

import java.util.List;

public interface PdmRemote {
    List<PdmCatalogNode> searchTree(String containerOid, String containerModel);

    List<PdmCADStruct> searchAllECADNodeByContainerOid(String containerOid);

    PdmECADIteration searchECADIterationByOid(String sourceOid);

    List<PdmCADStruct> searchECADNodeByAssemblyDocumentOids(List<String> assemblyDocumentOids);

    List<PdmECADIteration> buildNodes(List<JSONObject> jsonObjects);

    void buildRels(List<ECADStructureDTO> structList);

    void fileUploadCallback(List<JSONObject> jsonObjects);

    PdmTreeStructure findSecBom(String rootOid);

    PdmTreeStructure batchGetCadDocByOid(String oid);

    List<PdmECADFile> getFileList(List<String> allPmdECADIntegrationOids);

    List<PdmCatalog> findAllFolderByOid(String catalogOid);

    JSONObject checkout(ModelInfo modelInfo);

    void cancelCheckOut(ModelInfo modelInfo);

    JSONObject findNextVersionNode(ECADParam param);

    JSONObject globalSearch(JSONObject jsonParams);

    PdmECADIteration ecadCheckin(ECADIntegrationDTO integrationDTO);

    List<PdmContainer> queryAllPdmContainer();

    List<PdmPCBFile> getEcadFileList(List<String> collect);

    Object initOrUpdateLibrary(String path);

    Object searchCatalogTree();

    Object searchSchematic();

    List<PdmCADInfo> getCadInfoByNumbers(List<String> numbers, boolean pathFlag, boolean cadFileFlag);

    Object syncToolData(Object object);
}
