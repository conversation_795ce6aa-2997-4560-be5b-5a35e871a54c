package cn.jwis.platform.space.ecad.remote;


import cn.jwis.framework.base.domain.able.info.ModelInfo;
import com.alibaba.fastjson.JSONObject;

import java.util.List;

public interface PdmRemote {
    List<PdmCatalogNode> searchTree(String containerOid, String containerModel);

    List<PdmCADStruct> searchAllECADNodeByContainerOid(String containerOid);

    PdmECADIteration searchECADIterationByOid(String sourceOid);

    List<PdmCADStruct> searchECADNodeByAssemblyDocumentOids(List<String> assemblyDocumentOids);

    List<PdmECADIteration> buildNodes(List<JSONObject> jsonObjects);

    void buildRels(List<ECADStructureDTO> structList);

    void fileUploadCallback(List<JSONObject> jsonObjects);

    PdmTreeStructure findSecBom(String rootOid);

    PdmTreeStructure batchGetCadDocByOid(String oid);

    List<PdmECADFile> getFileList(List<String> allPmdECADIntegrationOids);

    List<PdmCatalog> findAllFolderByOid(String catalogOid);

    JSONObject checkout(ModelInfo modelInfo);

    void cancelCheckOut(ModelInfo modelInfo);

    JSONObject findNextVersionNode(ECADParam param);

    JSONObject globalSearch(JSONObject jsonParams);

    PdmECADIteration ecadCheckin(ECADIntegrationDTO integrationDTO);

    List<PdmContainer> queryAllPdmContainer();

    List<PdmPCBFile> getEcadFileList(List<String> collect);

    Object initOrUpdateLibrary(String path);

    Object searchCatalogTree();

    Object searchSchematic();

    List<PdmCADInfo> getCadInfoByNumbers(List<String> numbers, boolean pathFlag, boolean cadFileFlag);

    Object syncToolData(Object object);

    /**
     * 根据用户账号查询用户信息
     * @param account 用户账号
     * @return 用户信息
     */
    PdmUserInfo getUserByAccount(String account);

    /**
     * 根据用户OID查询用户信息
     * @param userOid 用户OID
     * @return 用户信息
     */
    PdmUserInfo getUserByOid(String userOid);

    /**
     * 获取用户认证信息(包含token)
     * @param params 查询参数
     * @return 用户认证信息
     */
    PdmUserAuthInfo getUserAuthInfo(PdmUserQueryParams params);

    /**
     * 验证用户token有效性
     * @param accessToken 访问令牌
     * @return 是否有效
     */
    Boolean validateToken(String accessToken);

    /**
     * 刷新用户token
     * @param refreshToken 刷新令牌
     * @return 新的认证信息
     */
    PdmUserAuthInfo refreshToken(String refreshToken);
}
