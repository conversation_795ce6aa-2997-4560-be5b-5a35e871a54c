package cn.jwis.platform.space.ecad.remote;

import cn.jwis.framework.base.domain.entity.BaseEntity;
import cn.jwis.platform.space.ecad.entity.File;
import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;


@Data
public class PdmECADIteration extends BaseEntity {
    public static final String TYPE = "ECADIteration";

    @ApiModelProperty("名称")
    private String name;

    @ApiModelProperty("编码")
    private String number;

    @ApiModelProperty("文档类型")
    private String docType;

    @ApiModelProperty("扩展属性")
    private JSONObject extensionContent;

    @ApiModelProperty("描述")
    private String description;

    @ApiModelProperty("主文件")
    private List<File> primaryFile;

    @ApiModelProperty("附件")
    private List<File> secondaryFile;


    //缩略图，产品图

    // ********** 版本 相关属性
    @ApiModelProperty("大版本")
    private String version;

    @ApiModelProperty("迭代版本")
    private int iteratedVersion;

    @ApiModelProperty("是否最新版本")
    private boolean latest;

    @ApiModelProperty(value = "版本序号", notes = "用于版本排序的序号")
    private int vesionSortId;

    @ApiModelProperty(value = "显示版本")
    private String displayVersion;

    // *********** 锁 相关属性
    @ApiModelProperty("挂锁 备注")
    private String lockNote;

    @ApiModelProperty("挂锁者 账号")
    private String lockOwnerAccount;

    @ApiModelProperty("挂锁者 oid")
    private String lockOwnerOid;

    @ApiModelProperty("挂锁时间")
    private Long lockedTime;

    @ApiModelProperty("挂锁时间")
    private String lockSourceOid;

    // *********** 生命周期 相关属性
    @ApiModelProperty("生命周期模板 oid")
    private String lifecycleOid;

    @ApiModelProperty("生命周期状态")
    private String lifecycleStatus;


    // ********** 容器 相关属性
    @ApiModelProperty("容器名称")
    private String containerName;

    @ApiModelProperty("容器 oid")
    private String containerOid;

    @ApiModelProperty("容器类型 ")
    private String containerType;

    @ApiModelProperty("目录 类型")
    private String catalogType;

    @ApiModelProperty("目录 oid")
    private String catalogOid;

    // ********** 分类 相关属性
    @ApiModelProperty("分类oid ")
    private String clsOid;

    @ApiModelProperty("分类 显示名称")
    private String clsDisplayName;

    @ApiModelProperty("分类编码")
    private String clsCode;

    @ApiModelProperty("分类属性")
    private JSONObject clsProperty;

    // ********** 密级 相关属性
    @ApiModelProperty("密级")
    private int levelForSecrecy;


    // ********** master 相关属性
    @ApiModelProperty("master 的 oid ")
    private String masterOid;

    @ApiModelProperty("master 的 类型")
    private String masterType;

    @ApiModelProperty("partNumber")
    private String partNumber;

    @ApiModelProperty("透明度")
    private String opacity;

    @ApiModelProperty("RGB")
    private String rgb;

}