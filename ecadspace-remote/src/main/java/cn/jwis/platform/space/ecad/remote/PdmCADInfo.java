package cn.jwis.platform.space.ecad.remote;

import cn.jwis.framework.base.domain.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class PdmCADInfo extends BaseEntity {

    private String catalogOid;
    private String catalogType;

    @NotBlank
    @ApiModelProperty("名称")
    private String name;
    @ApiModelProperty("编码")
    private String number;
    @ApiModelProperty("描述")
    private String description;
    @ApiModelProperty("是否最新版本")
    private boolean latest;
    @ApiModelProperty("版本的显示字段，用于前端展示")
    private String displayVersion;
    @ApiModelProperty("挂锁 备注")
    private String lockNote;
    @ApiModelProperty("挂锁者 账号")
    private String lockOwnerAccount;
    @ApiModelProperty("挂锁者 oid")
    private String lockOwnerOid;
    @ApiModelProperty("挂锁时间")
    private Long lockedTime;
    @ApiModelProperty("挂锁时间")
    private String lockSourceOid;
    @ApiModelProperty("生命周期模板oid")
    private String lifecycleOid;
    @ApiModelProperty("生命周期状态")
    private String lifecycleStatus;
    @ApiModelProperty("密级")
    private int levelForSecrecy;
    @ApiModelProperty("master的oid")
    private String masterOid;
    @ApiModelProperty("master类型")
    private String masterType;
    @ApiModelProperty("容器的oid")
    private String containerOid;
    @ApiModelProperty("容器的类型")
    private String containerType;

    private List<PdmCadFile> cadFiles;
    private List<PdmCatalog> folderPath;

    private List<PdmCADInfo> children;

}
