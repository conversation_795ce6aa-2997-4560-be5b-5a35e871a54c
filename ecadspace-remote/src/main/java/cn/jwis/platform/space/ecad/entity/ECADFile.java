package cn.jwis.platform.space.ecad.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * ECAD文件实体类
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ECADFile {
    
    /**
     * 文件OID
     */
    private String fileOid;
    
    /**
     * 文件名
     */
    private String fileName;
    
    /**
     * 文件类型
     */
    private String fileType;
    
    /**
     * 是否为主文件
     */
    private boolean primary;
    
    /**
     * 文件最后修改时间
     */
    private Date fileLastModified;
    
    /**
     * 文件URL
     */
    private String url;
}
