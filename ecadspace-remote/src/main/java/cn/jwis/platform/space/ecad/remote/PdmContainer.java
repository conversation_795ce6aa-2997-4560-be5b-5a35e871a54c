package cn.jwis.platform.space.ecad.remote;

import cn.jwis.framework.base.domain.entity.BaseEntity;
import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class PdmContainer extends BaseEntity {

    @ApiModelProperty("产品名称")
    private String name;

    @ApiModelProperty("产品型谱")
    private String productCatalogOid;
    @ApiModelProperty("产品型谱名称")
    private String productCatalogName;

    @ApiModelProperty("团队模板")
    private String teamTemplateOid;
    @ApiModelProperty("团队模板名称")
    private String teamTemplateName;

    @ApiModelProperty("产品容器模板")
    private String containerTemplateOid;

    @ApiModelProperty("产品容器模板名称")
    private String containerTemplateName;

    @ApiModelProperty("产品容器描述")
    private String description;

//    @ApiModelProperty("产品经理们")
//    private List<User> productManagers;

//    @ApiModelProperty("产品容器下的团队信息")
//    private Team team;

    private boolean privateFlag;

    @ApiModelProperty("扩展属性")
    private JSONObject extensionContent;
}
