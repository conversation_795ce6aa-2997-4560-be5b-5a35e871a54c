package cn.jwis.platform.space.ecad.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 文件实体类
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class File {
    
    /**
     * 文件OID
     */
    private String oid;
    
    /**
     * 文件名
     */
    private String name;
    
    /**
     * 文件类型
     */
    private String fileType;
    
    /**
     * 是否为主文件
     */
    private boolean primary;
    
    /**
     * 文件最后修改时间
     */
    private String lastModified;
    
    /**
     * 文件URL
     */
    private String url;
}
