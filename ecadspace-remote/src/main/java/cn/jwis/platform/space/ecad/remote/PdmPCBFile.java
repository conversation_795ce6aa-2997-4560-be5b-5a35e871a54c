package cn.jwis.platform.space.ecad.remote;

import cn.jwis.platform.space.ecad.entity.File;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class PdmPCBFile {

    private String nodeName;

    private List<File> primaryFiles = new ArrayList<>();
    private List<File> secondaryFiles = new ArrayList<>();
    private List<File> netList = new ArrayList<>();

    private String bomDataOid;
}
