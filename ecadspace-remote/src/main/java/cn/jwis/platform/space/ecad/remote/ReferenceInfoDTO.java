package cn.jwis.platform.space.ecad.remote;

import lombok.Data;

import java.util.Map;

@Data
public class ReferenceInfoDTO {
    private String oid;
    private String nodeName;
    private String state;
    private String type;
    private String pnumber;
    private String opacity;
    private String rgb;
    private String projName;
    private String pdmVersion;

    /**
     * cad的属性
     */
    private Map<String,Object> params;
    /**
     * cad的形状
     */
    private Map<String,Object> shapeInfo;

    private Map<String,Object>  pmiInfo;

    private Map<String,Object> annotationInfo;
}
