package cn.jwis.platform.space.ecad.remote;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class PdmECADFile {

    private String nodeName;

    @ApiModelProperty("文件名")
    private String fileName;

    @ApiModelProperty("其实他是fileOid, o_o")
    private String url;

    @ApiModelProperty("文件类型")
    private String fileType;

    @ApiModelProperty("是否主文件")
    private boolean primary;

    private String lastModified;
}
