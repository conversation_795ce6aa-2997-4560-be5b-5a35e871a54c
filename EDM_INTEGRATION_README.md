# EDM文件压缩包接入并检入space接口实现

## 概述

本文档描述了EDM文件压缩包接入并检入space的接口实现。该接口允许用户上传包含各类EDA文件的压缩包，系统会自动解压、分类、上传到Minio，并最终构造ECADIntegrationDTO进行检入。

## 接口信息

- **接口地址**: `POST /space-ecad-server/integration/edmIntegrationCheckin`
- **请求方式**: `multipart/form-data`

## 请求参数

| 字段名 | 类型 | 是否必填 | 说明 |
|--------|------|----------|------|
| meta | JSON字符串 | 是 | 元数据信息，用于映射到ECADIntegrationDTO |
| file | MultipartFile | 是 | 压缩包文件（zip），内部需包含分类型的文件夹 |

## meta JSON格式

```json
{
  "projectName": "MobilePhone2025",
  "ecadNumber": "ECAD-001", 
  "ecadName": "MainBoard_V1",
  "modelType": "PCB",
  "toolType": "AltiumDesigner",
  "toolVersion": "24.0",
  "checkin": true
}
```

## zip文件结构要求

压缩包内部需按文件类型建立子目录，系统根据目录名识别文件类型：

```
SCHEMATIC/         # 原理图文件
    main.sch
    schematic.pdf
BOM/               # BOM文件
    bom.xlsx
GERBER/            # Gerber文件
    xxx.gbr
STEP/              # 3D模型文件
    model.step
PDF/               # PDF文档
    drawing.pdf
ODB++/             # ODB++文件
    board.odb
```

## 文件类型映射

系统会根据压缩包中的目录名自动映射到ECADIntegrationDTO的相应字段：

- **SCHEMATIC** → `localPathOid` (主文件)
- **BOM** → `bomDataOid` (BOM文件)
- **GERBER** → `visiableOid` (轻量化文件)
- **STEP** → `productInfoPathOid` (产品信息文件)
- **PDF** → `packagePathOid` (包装文件)
- **ODB++** → `netList` (网表文件)

## 实现流程

1. **解析元数据**: 将meta JSON字符串解析为ECADIntegrationDTO对象
2. **解压文件**: 解压zip文件并按目录名分类文件
3. **上传文件**: 将每个文件上传到Minio获取fileOid
4. **构造DTO**: 根据文件类型映射构造完整的ECADIntegrationDTO
5. **执行检入**: 调用ECADService.saveAndCheckin方法完成检入

## 返回值

```json
{
  "code": 0,
  "msg": "success", 
  "data": null
}
```

或

```json
{
  "code": -1,
  "msg": "error message",
  "data": null
}
```

## 核心实现类

- **IntegrationController**: 控制器层，处理HTTP请求
- **IntegrationService**: 服务层接口
- **IntegrationServiceImpl**: 服务层实现，包含核心业务逻辑

## 主要方法

### IntegrationServiceImpl.edmIntegrationCheckin()

主要的业务处理方法，包含以下步骤：

1. 解析元数据
2. 解压并分类文件
3. 构造ECADIntegrationDTO
4. 调用saveAndCheckin

### unzipAndClassifyFiles()

解压zip文件并按目录名分类文件的方法。

### uploadFileToMinio()

上传单个文件到Minio的方法，返回文件OID。

### buildECADIntegrationDTO()

根据元数据和分类后的文件构造ECADIntegrationDTO的方法。

## 配置要求

需要在application.yml中配置文件服务网关地址：

```yaml
file:
  service:
    gateway:
      url: http://your-file-service-url
```

## 注意事项

1. 压缩包中的文件必须按指定的目录结构组织
2. 文件上传依赖于文件服务网关的可用性
3. 系统会自动清理上传过程中产生的临时文件
4. 如果文件上传失败，整个检入过程会回滚

## 错误处理

系统会捕获并处理以下类型的错误：

- JSON解析错误
- 文件解压错误  
- 文件上传错误
- 检入过程错误

所有错误都会返回统一的错误响应格式。
