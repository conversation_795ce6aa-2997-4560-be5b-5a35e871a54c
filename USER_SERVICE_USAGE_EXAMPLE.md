# 用户服务使用示例

## 概述

本文档展示如何在Space系统中使用新实现的用户服务来获取用户信息和token。

## 在Service中使用

### 1. 注入UserService

```java
@Service
@RequiredArgsConstructor
public class YourServiceImpl {
    
    private final UserService userService;
    
    // 你的业务方法
}
```

### 2. 获取当前用户信息

```java
public void someBusinessMethod() {
    // 方式1: 直接获取当前用户信息(推荐)
    PdmUserInfo currentUser = userService.getCurrentUser();
    if (currentUser != null) {
        String userOid = currentUser.getOid();
        String account = currentUser.getAccount();
        String userName = currentUser.getName();
        // 使用用户信息...
    }
    
    // 方式2: 获取当前用户的访问令牌
    String accessToken = userService.getCurrentUserAccessToken();
    if (accessToken != null) {
        // 使用token调用其他服务...
    }
}
```

### 3. 查询指定用户信息

```java
public void queryUserInfo() {
    // 根据账号查询
    PdmUserInfo user1 = userService.getUserByAccount("zhangsan");
    
    // 根据OID查询
    PdmUserInfo user2 = userService.getUserByOid("user123456");
}
```

### 4. 获取用户认证信息

```java
public void getUserAuthInfo() {
    PdmUserQueryParams params = new PdmUserQueryParams();
    params.setAccount("zhangsan");
    params.setIncludeAuthInfo(true);
    params.setIncludeRoles(true);
    
    PdmUserAuthInfo authInfo = userService.getUserAuthInfo(params);
    if (authInfo != null) {
        String accessToken = authInfo.getAccessToken();
        String refreshToken = authInfo.getRefreshToken();
        PdmUserInfo userInfo = authInfo.getUserInfo();
        // 使用认证信息...
    }
}
```

### 5. Token验证和刷新

```java
public void handleToken() {
    String token = "your_access_token";
    
    // 验证token有效性
    Boolean isValid = userService.validateToken(token);
    if (!isValid) {
        // token无效，需要刷新
        String refreshToken = "your_refresh_token";
        PdmUserAuthInfo newAuthInfo = userService.refreshToken(refreshToken);
        if (newAuthInfo != null) {
            String newAccessToken = newAuthInfo.getAccessToken();
            // 使用新的token...
        }
    }
}
```

## 在Controller中使用

```java
@RestController
@RequiredArgsConstructor
public class YourController {
    
    private final UserService userService;
    
    @GetMapping("/current-user")
    public Result<PdmUserInfo> getCurrentUser() {
        PdmUserInfo user = userService.getCurrentUser();
        return Result.success(user);
    }
    
    @GetMapping("/user-token")
    public Result<String> getCurrentUserToken() {
        String token = userService.getCurrentUserAccessToken();
        return Result.success(token);
    }
}
```

## 替换现有的SessionHelper调用

### 原来的代码

```java
// 原来的方式
String userOid = SessionHelper.getCurrentUser().getOid();
String accessToken = SessionHelper.getAccessToken();
```

### 新的方式

```java
// 新的方式 - 更安全，有异常处理
PdmUserInfo currentUser = userService.getCurrentUser();
String userOid = currentUser != null ? currentUser.getOid() : null;

String accessToken = userService.getCurrentUserAccessToken();
```

## 错误处理

```java
public void handleErrors() {
    try {
        PdmUserInfo user = userService.getCurrentUser();
        if (user == null) {
            // 处理用户信息为空的情况
            log.warn("无法获取当前用户信息");
            return;
        }
        
        // 正常业务逻辑
        
    } catch (Exception e) {
        log.error("获取用户信息失败", e);
        // 处理异常情况
    }
}
```

## 配置说明

确保在application.yml中配置了PDM用户服务地址：

```yaml
pdm:
  user:
    service:
      gateway:
        url: http://your-pdm-user-service-url
```

## 注意事项

1. **空值检查**: 所有用户服务方法都可能返回null，使用前请检查
2. **异常处理**: 网络调用可能失败，建议添加try-catch
3. **缓存考虑**: 如果频繁调用，考虑添加本地缓存
4. **性能优化**: getCurrentUser()方法会调用远程服务，避免在循环中使用
5. **安全性**: token信息敏感，不要记录到日志中

## 最佳实践

1. **一次获取，多次使用**:
```java
// 好的做法
PdmUserInfo currentUser = userService.getCurrentUser();
String userOid = currentUser.getOid();
String userName = currentUser.getName();

// 避免多次调用
// String userOid = userService.getCurrentUser().getOid();
// String userName = userService.getCurrentUser().getName();
```

2. **缓存用户信息**:
```java
@Service
public class CachedUserService {
    
    private final UserService userService;
    private final Cache<String, PdmUserInfo> userCache;
    
    public PdmUserInfo getCachedUser(String userOid) {
        return userCache.get(userOid, () -> userService.getUserByOid(userOid));
    }
}
```

3. **统一错误处理**:
```java
@Component
public class UserInfoHelper {
    
    private final UserService userService;
    
    public String getCurrentUserOidSafely() {
        try {
            PdmUserInfo user = userService.getCurrentUser();
            return user != null ? user.getOid() : null;
        } catch (Exception e) {
            log.error("获取当前用户OID失败", e);
            return null;
        }
    }
}
```
