package cn.jwis.platform.space.ecad.service.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Data
public class ECADCreateParams {

    private String oid;

    @ApiModelProperty("节点名字, 这玩意唯一，不用管")
    private String nodeName;
    private String state;
    private String type;
    private String pNumber;
    private String opacity;
    private String rgb;

    private String subType;
    private String refNodeName;

    private boolean dsVersionModified;

    @ApiModelProperty("文件夹路径，根据这玩意匹配文件夹oid")
    private List<String> projName = new ArrayList<>();
    private String pdmVersion;
    private Map<String, Object> params;
    private Map<String, Object> shapeInfo;
    private Map<String, Object> pmiInfo;
    private Map<String, Object> annotationInfo;

}
