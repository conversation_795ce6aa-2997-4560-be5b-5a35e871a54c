package cn.jwis.platform.space.ecad.service.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

@Data
public class BomFileData {

    @ExcelProperty(value = "VALUE", index = 5)
    private String partvalue;      //"104",                               resource 加个value

    @ExcelProperty(value = "DESCRIPTION", index = 7)
    private String partname;	   //"陶瓷电容-0603-X7R-0.1uF-±10%-25V",   resource name

    @ExcelProperty(value = "PART NUMBER", index = 3)
    private String partnumber;     //"CM0116M023",                        resource number    // 这个是唯一的，可以用来做校验

    @ExcelProperty(value = "REFDES", index = 2)
    private String partreference;  //"C1",                                relation  resource_location_number

    @ExcelProperty(value = "PACKAGE", index = 4)
    private String pcbfootprint;   //"C0603",                             封装name

    @ExcelProperty(value = "QTY", index = 1)
    private Integer quantity;       // 1                                   relation count

}

