package cn.jwis.platform.space.ecad.service.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

@Data
public class ADBomFileData {

    // AltiumDesigner

    @ExcelProperty(value = "编码", index = 0)
    private String partnumber;

    @ExcelProperty(value = "名称", index = 1)
    private String comment;

    @ExcelProperty(value = "数量", index = 2)
    private String quantity;

    @ExcelProperty(value = "位号", index = 3)
    private String position;


    // *******和之前的映射上******
    public String getPartname() {
        return comment;
    }

    public String getPartreference() {
        return position;
    }

}



