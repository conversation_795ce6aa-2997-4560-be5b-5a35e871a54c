package cn.jwis.platform.space.ecad.service.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class ECADFileCreateParams {

    @ApiModelProperty("文件oid")
    private String fileOid;

    @ApiModelProperty("文件名")
    private String fileName;

    @ApiModelProperty("文件修改时间")
    private Date fileLastModified;

    @ApiModelProperty("是否主文件")
    private boolean primary;

}
