package cn.jwis.platform.space.ecad.service.impl;

import cn.jwis.framework.base.util.OidGenerator;
import cn.jwis.framework.base.util.StringUtil;
import cn.jwis.framework.base.web.session.SessionHelper;
import cn.jwis.platform.space.ecad.ECADAndECADIntegration;
import cn.jwis.platform.space.ecad.domain.interf.ECADCatalogDomainService;
import cn.jwis.platform.space.ecad.domain.interf.ECADDomainService;
import cn.jwis.platform.space.ecad.domain.interf.ECADIterationDomainService;
import cn.jwis.platform.space.ecad.entity.ECAD;
import cn.jwis.platform.space.ecad.entity.ECADCatalog;
import cn.jwis.platform.space.ecad.entity.ECADIteration;
import cn.jwis.platform.space.ecad.entity.File;
import cn.jwis.platform.space.ecad.remote.*;
import cn.jwis.platform.space.ecad.service.dto.*;
import cn.jwis.platform.space.ecad.service.interf.IntegrationService;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

import static java.util.stream.Collectors.toList;
@Slf4j
@Service
@RequiredArgsConstructor
public class IntegrationServiceImpl implements IntegrationService {

    private final ECADDomainService ecadDomainService;
    private final ECADIterationDomainService ecadIterationDomainService;
    private final ECADCatalogDomainService ecadCatalogDomainService;
    private final PdmRemote pdmRemote;

    @Override
    public SyncResDTO sync(SyncParams params) {
        String userOid = SessionHelper.getCurrentUser().getOid();
        ECADCatalog ecadCatalog = ecadCatalogDomainService.findByOid(params.getCatalogOid());

        List<PdmCADInfo> pdmCADInfos = pdmRemote.getCadInfoByNumbers(params.getNumbers(), true, true);
        log.info("sync result:{}", JSONObject.toJSON(pdmCADInfos));
        List<String> allPmdECADIntegrationOids = new ArrayList<>();

        for (PdmCADInfo cadInfo : pdmCADInfos) {
            allPmdECADIntegrationOids.add(cadInfo.getOid());
            createECADAndECADIterations(cadInfo, ecadCatalog);
        }

        List<ECADFileDTO> list = downloadFiles(allPmdECADIntegrationOids, userOid, ecadCatalog);

        List<List<String>> folders = new ArrayList<>();
        folders.add(Lists.newArrayList(ecadCatalog.getName()));

        return new SyncResDTO(list, folders);
    }

    @Override
    public List<ECADFileDTO> downloadFiles(List<String> allPmdECADIntegrationOids, String userOid, ECADCatalog ecadCatalog) {
        List<PdmPCBFile> fileList = pdmRemote.getEcadFileList(allPmdECADIntegrationOids.stream().distinct().collect(toList()));
        System.out.println("fileList: " + JSONArray.toJSONString(fileList));
        if (fileList == null || fileList.isEmpty()) {
            return new ArrayList<>();
        }

        List<ECADFileDTO> result = new ArrayList<>();
        for (PdmPCBFile pdmEcadFile : fileList) {
            ECADIteration ecadIteration = ecadIterationDomainService.findLatestECADIterationByName(userOid, pdmEcadFile.getNodeName(), ecadCatalog.getSpaceContainerOid());

            List<File> primaryFiles = pdmEcadFile.getPrimaryFiles();
            if (!primaryFiles.isEmpty()) {
                ecadIteration.setLocalPathOid(primaryFiles.get(0).getOid());
                result.addAll(getEcadFileDTOS(Lists.newArrayList(ecadCatalog.getName()), primaryFiles));
            }

            List<File> secondaryFiles = pdmEcadFile.getSecondaryFiles();
            if (!secondaryFiles.isEmpty()) {
                ecadIteration.setVisiableOid(secondaryFiles.get(0).getOid());
                ecadIteration.setVisiableName(secondaryFiles.get(0).getName());

                for (File file : secondaryFiles) {
                    if ("schematicProFile".equalsIgnoreCase(file.getFileType())) {
                        ecadIteration.setPackagePathOid(file.getOid());
                        ecadIteration.setPackagePathName(file.getName());
                        result.addAll(getEcadFileDTOS(Lists.newArrayList(ecadCatalog.getName()), Lists.newArrayList(file)));
                    }
                }
            }

            List<File> netList = pdmEcadFile.getNetList();
            if (!netList.isEmpty()) {
                ecadIteration.setNetList(netList);
                result.addAll(getEcadFileDTOS(Lists.newArrayList(ecadCatalog.getName(), ecadIteration.getName() + ECADConstant.ALLEGRO), netList));
            }

            ecadIteration.setBomDataOid(pdmEcadFile.getBomDataOid());

            ecadIterationDomainService.updateLockByOid(ecadIteration);
        }
        return result;
    }

    private List<ECADFileDTO> getEcadFileDTOS(List<String> dirPath, List<File> primaryFiles) {
        return primaryFiles.stream().map(file -> {
            ECADFileDTO ecadFileDTO = new ECADFileDTO();
            ecadFileDTO.setFileName(file.getName());
            ecadFileDTO.setFileOid(file.getOid());
            ecadFileDTO.setDirPath(dirPath);
            return ecadFileDTO;
        }).collect(toList());
    }

    public ECADAndECADIntegration createECADAndECADIterations(PdmCADInfo cadInfo, ECADCatalog ecadCatalog) {
        String userOid = SessionHelper.getCurrentUser().getOid();

        ECAD ecad = ecadDomainService.queryECADByName(userOid, cadInfo.getName(), ecadCatalog.getSpaceContainerOid());
        if (ecad != null) {
            ECADIteration ecadIteration = ecadIterationDomainService.queryLatestIterationsByECADOid(ecad.getOid());
            setLockAndLifecycle(ecadIteration, cadInfo);
            if (ecadIteration.getPdmVersion().equals(cadInfo.getDisplayVersion())) {
                log.info("createECADAndECADIterations version eq updateLocal ecad number:{}  sourceOid:{}",
                        ecadIteration.getPnumber(),cadInfo.getOid());
                ecadIteration.setSourceOid(cadInfo.getOid());
                ecadIterationDomainService.updateLockByOid(ecadIteration);
            } else {
                log.info("createECADAndECADIterations version not eq updateLocal ecad number:{}  sourceOid:{}",
                        ecadIteration.getPnumber(),cadInfo.getOid());
                setVersion(ecadIteration, cadInfo);
                ecadIterationDomainService.updateLatestIsFalseByOid(ecadIteration.getOid());
                ecadIteration.setOid(OidGenerator.newOid());
                ecadIteration.setSourceOid(cadInfo.getOid());
                ecadIterationDomainService.create(ecadIteration);
                ecadDomainService.createIterateRelationship(ecad.getOid(), ecadIteration.getOid());
            }
            return new ECADAndECADIntegration(ecad, ecadIteration);
        }

        ECAD newECAD = new ECAD();
        newECAD.setOid(OidGenerator.newOid());
        newECAD.setUserOid(userOid);
        newECAD.setName(cadInfo.getName());
        newECAD.setContainerOid(cadInfo.getContainerOid());
        newECAD.setModelDefinition(cadInfo.getModelDefinition());
        newECAD.setSpaceContainerOid(ecadCatalog.getSpaceContainerOid());
        ecadDomainService.create(newECAD);

        ECADIteration dbEcadIteration = ecadIterationDomainService.create(buildECADIteration(cadInfo, userOid, ecadCatalog.getSpaceContainerOid()));
        ecadDomainService.createIterateRelationship(newECAD.getOid(), dbEcadIteration.getOid());

        ecadCatalogDomainService.createContainECADRelationship(ecadCatalog.getOid(), newECAD.getOid());

        return new ECADAndECADIntegration(newECAD, dbEcadIteration);
    }

    private void setVersion(ECADIteration ecadIteration, PdmCADInfo cadInfo) {
//        mcadIteration.setSourceOid(cadInfo.getOid());
        ecadIteration.setPdmLatestVersion(cadInfo.getDisplayVersion());
        ecadIteration.setPdmVersion(cadInfo.getDisplayVersion());

        List<PdmCatalog> pdmCatalogs = pdmRemote.findAllFolderByOid(cadInfo.getCatalogOid());
        ecadIteration.setPdmCatalogFullPaths(pdmCatalogs.stream().map(PdmCatalog::getName).collect(toList()));

        ecadIteration.setIteratedVersion(ecadIteration.getIteratedVersion() + 1);
        ecadIteration.setDisplayVersion("V." + ecadIteration.getIteratedVersion());
        ecadIteration.setSpaceLifecycleCode(SpaceLifecycleEnum.NORMAL.getCode());
        ecadIteration.setSpaceLifecycleStatus(SpaceLifecycleEnum.NORMAL.getStatus());
    }

    private void setLockAndLifecycle(ECADIteration ecadIteration, PdmCADInfo cadInfo) {
        if (StringUtil.isNotBlank(cadInfo.getLockOwnerAccount())) {
            ecadIteration.setLockNote(cadInfo.getLockNote());
            ecadIteration.setLockOwnerAccount(cadInfo.getLockOwnerAccount());
            ecadIteration.setLockOwnerOid(cadInfo.getLockOwnerOid());
            ecadIteration.setLockedTime(cadInfo.getLockedTime());
            ecadIteration.setLockSourceOid(cadInfo.getOid());

            ecadIteration.setSourceOid(cadInfo.getLockSourceOid());
        } else {
            ecadIteration.setLockNote(null);
            ecadIteration.setLockOwnerAccount(null);
            ecadIteration.setLockOwnerOid(null);
            ecadIteration.setLockedTime(null);
            ecadIteration.setLockSourceOid(null);
        }
        ecadIteration.setLifecycleOid(cadInfo.getLifecycleOid());
        ecadIteration.setLifecycleStatus(cadInfo.getLifecycleStatus());
    }

    private ECADCatalog getEcadCatalog(PdmCatalog catalog, boolean top) {
        ECADCatalog ecadCatalog = new ECADCatalog();
        ecadCatalog.setOid(OidGenerator.newOid());
        ecadCatalog.setName(catalog.getName());
        ecadCatalog.setDownloaded(true);
        ecadCatalog.setTop(top);
        ecadCatalog.setUserOid(SessionHelper.getCurrentUser().getOid());
        ecadCatalog.setContainerOid(catalog.getContainerOid());
        ecadCatalog.setContainerType(catalog.getContainerType());
        ecadCatalog.setContainerModel(catalog.getContainerModel());
        return ecadCatalog;
    }

    public ECADIteration buildECADIteration(PdmCADInfo cadInfo, String userOid, String spaceContainerOid) {
        ECADIteration ecadIteration = new ECADIteration();
        ecadIteration.setOid(OidGenerator.newOid());
        ecadIteration.setName(cadInfo.getName());
        ecadIteration.setModelDefinition(cadInfo.getModelDefinition());
        ecadIteration.setPnumber(cadInfo.getNumber());
        ecadIteration.setNumber(cadInfo.getNumber());
        ecadIteration.setPdmVersion(cadInfo.getDisplayVersion());
        ecadIteration.setPdmLatestVersion(cadInfo.getDisplayVersion());
        ecadIteration.setPdmType(cadInfo.getModelDefinition());

        ecadIteration.setProjectName(cadInfo.getName());
        ecadIteration.setEcadNumber(cadInfo.getNumber());
        ecadIteration.setEcadName(cadInfo.getName());
        ecadIteration.setModelType(cadInfo.getModelDefinition());
//        ecadIteration.setProductPath();
//        ecadIteration.setNetList();
        ecadIteration.setLocalPathOid("");
//        ecadIteration.setBomDataOid();
//        ecadIteration.setSchematicNumber();
        ecadIteration.setVisiableOid("");

        ecadIteration.setContainerOid(cadInfo.getContainerOid());
        ecadIteration.setContainerType(cadInfo.getContainerType());

        ecadIteration.setLifecycleOid(cadInfo.getLifecycleOid());
        ecadIteration.setLifecycleStatus(cadInfo.getLifecycleStatus());

        ecadIteration.setSourceOid(cadInfo.getOid());

        ecadIteration.setUserOid(userOid);
        ecadIteration.setVersion("V.");
        ecadIteration.setLatest(true);
        ecadIteration.setIteratedVersion(1L);
        ecadIteration.setDisplayVersion("V." + 1);

        ecadIteration.setCheckin(true);

        if (StringUtil.isNotBlank(cadInfo.getLockOwnerAccount())) {
            ecadIteration.setLockNote(cadInfo.getLockNote());
            ecadIteration.setLockOwnerAccount(cadInfo.getLockOwnerAccount());
            ecadIteration.setLockOwnerOid(cadInfo.getLockOwnerOid());
            ecadIteration.setLockedTime(cadInfo.getLockedTime());
            ecadIteration.setLockSourceOid(cadInfo.getOid());

            ecadIteration.setSourceOid(cadInfo.getLockSourceOid());
        }

        ecadIteration.setLockNote(cadInfo.getLockNote());
        ecadIteration.setLockOwnerAccount(cadInfo.getLockOwnerAccount());
        ecadIteration.setLockOwnerOid(cadInfo.getLockOwnerOid());
        ecadIteration.setLockedTime(cadInfo.getLockedTime());
        ecadIteration.setLockSourceOid(cadInfo.getLockSourceOid());

        ecadIteration.setSpaceContainerOid(spaceContainerOid);

        List<PdmCatalog> pdmCatalogs = pdmRemote.findAllFolderByOid(cadInfo.getCatalogOid());
        ecadIteration.setPdmCatalogFullPaths(pdmCatalogs.stream().map(PdmCatalog::getName).collect(toList()));
        return ecadIteration;
    }

    private ECADCatalog buildECADCatalog(PdmCatalogNode node, String containerModel, boolean top, String spaceContainerOid) {
        ECADCatalog ecadCatalog = new ECADCatalog();
        ecadCatalog.setOid(OidGenerator.newOid());
        ecadCatalog.setName(node.getName());
        ecadCatalog.setDownloaded(true);
        ecadCatalog.setTop(top);
        ecadCatalog.setUserOid(SessionHelper.getCurrentUser().getOid());
        ecadCatalog.setContainerOid(node.getContainerOid());
        ecadCatalog.setContainerType(node.getContainerType());
        ecadCatalog.setContainerModel(containerModel);

        ecadCatalog.setSpaceContainerOid(spaceContainerOid);
        return ecadCatalog;
    }

}
