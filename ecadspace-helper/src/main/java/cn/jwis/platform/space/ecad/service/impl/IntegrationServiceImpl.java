package cn.jwis.platform.space.ecad.service.impl;

import cn.jwis.framework.base.dto.UserDTO;
import cn.jwis.framework.base.util.OidGenerator;
import cn.jwis.framework.base.util.StringUtil;
import cn.jwis.framework.base.web.session.SessionHelper;
import cn.jwis.platform.space.ecad.ECADAndECADIntegration;
import cn.jwis.platform.space.ecad.domain.interf.ECADCatalogDomainService;
import cn.jwis.platform.space.ecad.domain.interf.ECADDomainService;
import cn.jwis.platform.space.ecad.domain.interf.ECADIterationDomainService;
import cn.jwis.platform.space.ecad.entity.ECAD;
import cn.jwis.platform.space.ecad.entity.ECADCatalog;
import cn.jwis.platform.space.ecad.entity.ECADIteration;
import cn.jwis.platform.space.ecad.entity.File;
import cn.jwis.platform.space.ecad.remote.*;
import cn.jwis.platform.space.ecad.service.dto.*;
import cn.jwis.platform.space.ecad.service.interf.ECADService;
import cn.jwis.platform.space.ecad.service.interf.IntegrationService;
import cn.hutool.core.io.FileUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import java.io.*;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;

import static java.util.stream.Collectors.toList;
@Slf4j
@Service
@RequiredArgsConstructor
public class IntegrationServiceImpl implements IntegrationService {

    private final ECADDomainService ecadDomainService;
    private final ECADIterationDomainService ecadIterationDomainService;
    private final ECADCatalogDomainService ecadCatalogDomainService;
    private final PdmRemote pdmRemote;
    private final ECADService ecadService;

    @Value("${file.service.gateway.url}")
    private String fileServiceGatewayUrl;

    @Override
    public SyncResDTO sync(SyncParams params) {
        String userOid = SessionHelper.getCurrentUser().getOid();
        ECADCatalog ecadCatalog = ecadCatalogDomainService.findByOid(params.getCatalogOid());

        List<PdmCADInfo> pdmCADInfos = pdmRemote.getCadInfoByNumbers(params.getNumbers(), true, true);
        log.info("sync result:{}", JSONObject.toJSON(pdmCADInfos));
        List<String> allPmdECADIntegrationOids = new ArrayList<>();

        for (PdmCADInfo cadInfo : pdmCADInfos) {
            allPmdECADIntegrationOids.add(cadInfo.getOid());
            createECADAndECADIterations(cadInfo, ecadCatalog);
        }

        List<ECADFileDTO> list = downloadFiles(allPmdECADIntegrationOids, userOid, ecadCatalog);

        List<List<String>> folders = new ArrayList<>();
        folders.add(Lists.newArrayList(ecadCatalog.getName()));

        return new SyncResDTO(list, folders);
    }

    @Override
    public List<ECADFileDTO> downloadFiles(List<String> allPmdECADIntegrationOids, String userOid, ECADCatalog ecadCatalog) {
        List<PdmPCBFile> fileList = pdmRemote.getEcadFileList(allPmdECADIntegrationOids.stream().distinct().collect(toList()));
        System.out.println("fileList: " + JSONArray.toJSONString(fileList));
        if (fileList == null || fileList.isEmpty()) {
            return new ArrayList<>();
        }

        List<ECADFileDTO> result = new ArrayList<>();
        for (PdmPCBFile pdmEcadFile : fileList) {
            ECADIteration ecadIteration = ecadIterationDomainService.findLatestECADIterationByName(userOid, pdmEcadFile.getNodeName(), ecadCatalog.getSpaceContainerOid());

            List<File> primaryFiles = pdmEcadFile.getPrimaryFiles();
            if (!primaryFiles.isEmpty()) {
                ecadIteration.setLocalPathOid(primaryFiles.get(0).getOid());
                result.addAll(getEcadFileDTOS(Lists.newArrayList(ecadCatalog.getName()), primaryFiles));
            }

            List<File> secondaryFiles = pdmEcadFile.getSecondaryFiles();
            if (!secondaryFiles.isEmpty()) {
                ecadIteration.setVisiableOid(secondaryFiles.get(0).getOid());
                ecadIteration.setVisiableName(secondaryFiles.get(0).getName());

                for (File file : secondaryFiles) {
                    if ("schematicProFile".equalsIgnoreCase(file.getFileType())) {
                        ecadIteration.setPackagePathOid(file.getOid());
                        ecadIteration.setPackagePathName(file.getName());
                        result.addAll(getEcadFileDTOS(Lists.newArrayList(ecadCatalog.getName()), Lists.newArrayList(file)));
                    }
                }
            }

            List<File> netList = pdmEcadFile.getNetList();
            if (!netList.isEmpty()) {
                ecadIteration.setNetList(netList);
                result.addAll(getEcadFileDTOS(Lists.newArrayList(ecadCatalog.getName(), ecadIteration.getName() + ECADConstant.ALLEGRO), netList));
            }

            ecadIteration.setBomDataOid(pdmEcadFile.getBomDataOid());

            ecadIterationDomainService.updateLockByOid(ecadIteration);
        }
        return result;
    }

    private List<ECADFileDTO> getEcadFileDTOS(List<String> dirPath, List<File> primaryFiles) {
        return primaryFiles.stream().map(file -> {
            ECADFileDTO ecadFileDTO = new ECADFileDTO();
            ecadFileDTO.setFileName(file.getName());
            ecadFileDTO.setFileOid(file.getOid());
            ecadFileDTO.setDirPath(dirPath);
            return ecadFileDTO;
        }).collect(toList());
    }

    public ECADAndECADIntegration createECADAndECADIterations(PdmCADInfo cadInfo, ECADCatalog ecadCatalog) {
        String userOid = SessionHelper.getCurrentUser().getOid();

        ECAD ecad = ecadDomainService.queryECADByName(userOid, cadInfo.getName(), ecadCatalog.getSpaceContainerOid());
        if (ecad != null) {
            ECADIteration ecadIteration = ecadIterationDomainService.queryLatestIterationsByECADOid(ecad.getOid());
            setLockAndLifecycle(ecadIteration, cadInfo);
            if (ecadIteration.getPdmVersion().equals(cadInfo.getDisplayVersion())) {
                log.info("createECADAndECADIterations version eq updateLocal ecad number:{}  sourceOid:{}",
                        ecadIteration.getPnumber(),cadInfo.getOid());
                ecadIteration.setSourceOid(cadInfo.getOid());
                ecadIterationDomainService.updateLockByOid(ecadIteration);
            } else {
                log.info("createECADAndECADIterations version not eq updateLocal ecad number:{}  sourceOid:{}",
                        ecadIteration.getPnumber(),cadInfo.getOid());
                setVersion(ecadIteration, cadInfo);
                ecadIterationDomainService.updateLatestIsFalseByOid(ecadIteration.getOid());
                ecadIteration.setOid(OidGenerator.newOid());
                ecadIteration.setSourceOid(cadInfo.getOid());
                ecadIterationDomainService.create(ecadIteration);
                ecadDomainService.createIterateRelationship(ecad.getOid(), ecadIteration.getOid());
            }
            return new ECADAndECADIntegration(ecad, ecadIteration);
        }

        ECAD newECAD = new ECAD();
        newECAD.setOid(OidGenerator.newOid());
        newECAD.setUserOid(userOid);
        newECAD.setName(cadInfo.getName());
        newECAD.setContainerOid(cadInfo.getContainerOid());
        newECAD.setModelDefinition(cadInfo.getModelDefinition());
        newECAD.setSpaceContainerOid(ecadCatalog.getSpaceContainerOid());
        ecadDomainService.create(newECAD);

        ECADIteration dbEcadIteration = ecadIterationDomainService.create(buildECADIteration(cadInfo, userOid, ecadCatalog.getSpaceContainerOid()));
        ecadDomainService.createIterateRelationship(newECAD.getOid(), dbEcadIteration.getOid());

        ecadCatalogDomainService.createContainECADRelationship(ecadCatalog.getOid(), newECAD.getOid());

        return new ECADAndECADIntegration(newECAD, dbEcadIteration);
    }

    private void setVersion(ECADIteration ecadIteration, PdmCADInfo cadInfo) {
//        mcadIteration.setSourceOid(cadInfo.getOid());
        ecadIteration.setPdmLatestVersion(cadInfo.getDisplayVersion());
        ecadIteration.setPdmVersion(cadInfo.getDisplayVersion());

        List<PdmCatalog> pdmCatalogs = pdmRemote.findAllFolderByOid(cadInfo.getCatalogOid());
        ecadIteration.setPdmCatalogFullPaths(pdmCatalogs.stream().map(PdmCatalog::getName).collect(toList()));

        ecadIteration.setIteratedVersion(ecadIteration.getIteratedVersion() + 1);
        ecadIteration.setDisplayVersion("V." + ecadIteration.getIteratedVersion());
        ecadIteration.setSpaceLifecycleCode(SpaceLifecycleEnum.NORMAL.getCode());
        ecadIteration.setSpaceLifecycleStatus(SpaceLifecycleEnum.NORMAL.getStatus());
    }

    private void setLockAndLifecycle(ECADIteration ecadIteration, PdmCADInfo cadInfo) {
        if (StringUtil.isNotBlank(cadInfo.getLockOwnerAccount())) {
            ecadIteration.setLockNote(cadInfo.getLockNote());
            ecadIteration.setLockOwnerAccount(cadInfo.getLockOwnerAccount());
            ecadIteration.setLockOwnerOid(cadInfo.getLockOwnerOid());
            ecadIteration.setLockedTime(cadInfo.getLockedTime());
            ecadIteration.setLockSourceOid(cadInfo.getOid());

            ecadIteration.setSourceOid(cadInfo.getLockSourceOid());
        } else {
            ecadIteration.setLockNote(null);
            ecadIteration.setLockOwnerAccount(null);
            ecadIteration.setLockOwnerOid(null);
            ecadIteration.setLockedTime(null);
            ecadIteration.setLockSourceOid(null);
        }
        ecadIteration.setLifecycleOid(cadInfo.getLifecycleOid());
        ecadIteration.setLifecycleStatus(cadInfo.getLifecycleStatus());
    }

    private ECADCatalog getEcadCatalog(PdmCatalog catalog, boolean top) {
        ECADCatalog ecadCatalog = new ECADCatalog();
        ecadCatalog.setOid(OidGenerator.newOid());
        ecadCatalog.setName(catalog.getName());
        ecadCatalog.setDownloaded(true);
        ecadCatalog.setTop(top);
        ecadCatalog.setUserOid(SessionHelper.getCurrentUser().getOid());
        ecadCatalog.setContainerOid(catalog.getContainerOid());
        ecadCatalog.setContainerType(catalog.getContainerType());
        ecadCatalog.setContainerModel(catalog.getContainerModel());
        return ecadCatalog;
    }

    public ECADIteration buildECADIteration(PdmCADInfo cadInfo, String userOid, String spaceContainerOid) {
        ECADIteration ecadIteration = new ECADIteration();
        ecadIteration.setOid(OidGenerator.newOid());
        ecadIteration.setName(cadInfo.getName());
        ecadIteration.setModelDefinition(cadInfo.getModelDefinition());
        ecadIteration.setPnumber(cadInfo.getNumber());
        ecadIteration.setNumber(cadInfo.getNumber());
        ecadIteration.setPdmVersion(cadInfo.getDisplayVersion());
        ecadIteration.setPdmLatestVersion(cadInfo.getDisplayVersion());
        ecadIteration.setPdmType(cadInfo.getModelDefinition());

        ecadIteration.setProjectName(cadInfo.getName());
        ecadIteration.setEcadNumber(cadInfo.getNumber());
        ecadIteration.setEcadName(cadInfo.getName());
        ecadIteration.setModelType(cadInfo.getModelDefinition());
//        ecadIteration.setProductPath();
//        ecadIteration.setNetList();
        ecadIteration.setLocalPathOid("");
//        ecadIteration.setBomDataOid();
//        ecadIteration.setSchematicNumber();
        ecadIteration.setVisiableOid("");

        ecadIteration.setContainerOid(cadInfo.getContainerOid());
        ecadIteration.setContainerType(cadInfo.getContainerType());

        ecadIteration.setLifecycleOid(cadInfo.getLifecycleOid());
        ecadIteration.setLifecycleStatus(cadInfo.getLifecycleStatus());

        ecadIteration.setSourceOid(cadInfo.getOid());

        ecadIteration.setUserOid(userOid);
        ecadIteration.setVersion("V.");
        ecadIteration.setLatest(true);
        ecadIteration.setIteratedVersion(1L);
        ecadIteration.setDisplayVersion("V." + 1);

        ecadIteration.setCheckin(true);

        if (StringUtil.isNotBlank(cadInfo.getLockOwnerAccount())) {
            ecadIteration.setLockNote(cadInfo.getLockNote());
            ecadIteration.setLockOwnerAccount(cadInfo.getLockOwnerAccount());
            ecadIteration.setLockOwnerOid(cadInfo.getLockOwnerOid());
            ecadIteration.setLockedTime(cadInfo.getLockedTime());
            ecadIteration.setLockSourceOid(cadInfo.getOid());

            ecadIteration.setSourceOid(cadInfo.getLockSourceOid());
        }

        ecadIteration.setLockNote(cadInfo.getLockNote());
        ecadIteration.setLockOwnerAccount(cadInfo.getLockOwnerAccount());
        ecadIteration.setLockOwnerOid(cadInfo.getLockOwnerOid());
        ecadIteration.setLockedTime(cadInfo.getLockedTime());
        ecadIteration.setLockSourceOid(cadInfo.getLockSourceOid());

        ecadIteration.setSpaceContainerOid(spaceContainerOid);

        List<PdmCatalog> pdmCatalogs = pdmRemote.findAllFolderByOid(cadInfo.getCatalogOid());
        ecadIteration.setPdmCatalogFullPaths(pdmCatalogs.stream().map(PdmCatalog::getName).collect(toList()));
        return ecadIteration;
    }

    private ECADCatalog buildECADCatalog(PdmCatalogNode node, String containerModel, boolean top, String spaceContainerOid) {
        ECADCatalog ecadCatalog = new ECADCatalog();
        ecadCatalog.setOid(OidGenerator.newOid());
        ecadCatalog.setName(node.getName());
        ecadCatalog.setDownloaded(true);
        ecadCatalog.setTop(top);
        ecadCatalog.setUserOid(SessionHelper.getCurrentUser().getOid());
        ecadCatalog.setContainerOid(node.getContainerOid());
        ecadCatalog.setContainerType(node.getContainerType());
        ecadCatalog.setContainerModel(containerModel);

        ecadCatalog.setSpaceContainerOid(spaceContainerOid);
        return ecadCatalog;
    }

    @Override
    public JSONObject edmIntegrationCheckin(String metaJson, MultipartFile zipFile) {
        log.info("开始处理EDM文件压缩包检入, meta: {}, fileName: {}", metaJson, zipFile.getOriginalFilename());
        PdmUserQueryParams pdmUserQueryParams = new PdmUserQueryParams();
        pdmUserQueryParams.setAccount("yuchenghui");
        PdmUserAuthInfo userAuthInfo = pdmRemote.getUserAuthInfo(pdmUserQueryParams);
        String accessToken = userAuthInfo.getAccessToken();
        PdmUserInfo userInfo = userAuthInfo.getUserInfo();
        UserDTO currentUser = new UserDTO();
        currentUser.setOid(userInfo.getOid());
        currentUser.setAccount(userInfo.getAccount());
        currentUser.setTenantOid(userInfo.getTenantOid());
        SessionHelper.addCurrentUser(currentUser);
        log.info("accessToken: {}", accessToken);
        if(StringUtil.isNotBlank(accessToken)) {
            SessionHelper.setAccessToken(accessToken);
        }else {
            SessionHelper.setAccessToken("eyJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJKV0lTIiwiaWF0IjoxNjk4ODQwOTI4LCJzdWIiOiJzeXNfYWRtaW4iLCJleHAiOjE2OTk0NDU3MjgsInVzZXIiOnsib2lkIjoic3lzX2FkbWluIiwidGVuYW50T2lkIjoiIiwidGVuYW50TmFtZSI6bnVsbCwiYWNjb3VudCI6InN5c19hZG1pbiIsIm5hbWUiOiLns7vnu5_nrqHnkIblkZgiLCJwYXNzd29yZCI6bnVsbCwicGhvbmUiOm51bGwsImVtYWlsIjpudWxsLCJnZW5kZXIiOjAsImRlc2NyaXB0aW9uIjpudWxsLCJhdmF0YXIiOm51bGwsImRpc2FibGUiOjAsInNlY3VyaXR5TGV2ZWwiOjk5OSwiaWNvbiI6bnVsbCwib2xkUGFzc3dvcmQiOm51bGwsImNvbmZpcm1QYXNzd29yZCI6bnVsbCwibmV3UGFzc3dvcmQiOm51bGwsInRpcHMiOm51bGwsImludmFsaWRUaW1lIjpudWxsLCJjcmVhdGVCeSI6bnVsbCwic2lnbmF0dXJlIjpudWxsLCJjcmVhdGVEYXRlIjowLCJ1cGRhdGVCeSI6bnVsbCwidXBkYXRlRGF0ZSI6MH0sImFjY291bnQiOiJzeXNfYWRtaW4iLCJ1c2VyT2lkIjoic3lzX2FkbWluIn0.Mk5efWzWg03MUpHkw1YAHVJEcqC2RqDWIjC43U0ZSnI");
        }

        try {
            // 1. 解析元数据
            ECADIntegrationDTO meta = JSON.parseObject(metaJson, ECADIntegrationDTO.class);
            log.info("解析元数据成功: {}", JSON.toJSONString(meta));

            // 2. 解压文件并按类型分类
            Map<String, List<File>> filesByType = unzipAndClassifyFiles(zipFile);
            log.info("文件解压和分类完成，文件类型数量: {}", filesByType.size());

            // 3. 构造ECADIntegrationDTO
            ECADIntegrationDTO ecadIntegrationDTO = buildECADIntegrationDTO(meta, filesByType);
            log.info("构造ECADIntegrationDTO完成");
            log.info("ecadIntegrationDTO:{}", JSON.toJSONString(ecadIntegrationDTO));

            // 4. 调用saveAndCheckin
            JSONObject result = ecadService.saveAndCheckin(ecadIntegrationDTO);
//            JSONObject result = new JSONObject();
            log.info("EDM文件检入完成");

            return result;

        } catch (Exception e) {
            log.error("EDM文件检入失败", e);
            JSONObject errorResult = new JSONObject();
            errorResult.put("code", -1);
            errorResult.put("msg", "EDM文件检入失败: " + e.getMessage());
            errorResult.put("data", null);
            return errorResult;
        }
    }

    /**
     * 解压文件并按类型分类
     */
    private Map<String, List<File>> unzipAndClassifyFiles(MultipartFile zipFile) throws IOException {
        Map<String, List<File>> filesByType = new HashMap<>();

        try (ZipInputStream zipInputStream = new ZipInputStream(zipFile.getInputStream())) {
            ZipEntry entry;
            while ((entry = zipInputStream.getNextEntry()) != null) {
                if (!entry.isDirectory()) {
                    // 获取文件路径和文件名
                    String entryName = entry.getName();
                    String[] pathParts = entryName.split("/");

                    if (pathParts.length >= 2) {
                        String fileType = pathParts[0]; // 第一级目录作为文件类型
                        String fileName = pathParts[pathParts.length - 1]; // 最后一部分作为文件名

                        // 读取文件内容并上传到Minio
                        byte[] fileContent = readZipEntryContent(zipInputStream);
                        String fileOid = uploadFileToMinio(fileName, fileContent);

                        // 创建File对象
                        File file = new File();
                        file.setName(fileName);
                        file.setOid(fileOid);
                        file.setFileType(fileType);

                        // 按类型分类
                        filesByType.computeIfAbsent(fileType, k -> new ArrayList<>()).add(file);

                        log.info("处理文件: {} -> 类型: {}, OID: {}", fileName, fileType, fileOid);
                    }
                }
                zipInputStream.closeEntry();
            }
        }

        return filesByType;
    }

    /**
     * 读取ZIP条目内容
     */
    private byte[] readZipEntryContent(ZipInputStream zipInputStream) throws IOException {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        byte[] buffer = new byte[1024];
        int len;
        while ((len = zipInputStream.read(buffer)) > 0) {
            baos.write(buffer, 0, len);
        }
        return baos.toByteArray();
    }

    /**
     * 上传文件到Minio
     */
    private String uploadFileToMinio(String fileName, byte[] fileContent) throws IOException {
        try {
            // 创建临时文件
            String tempFilePath = System.getProperty("java.io.tmpdir") + java.io.File.separator + "edm_" + System.currentTimeMillis() + "_" + fileName;
            java.io.File tempFile = new java.io.File(tempFilePath);

            // 写入文件内容
            try (FileOutputStream fos = new FileOutputStream(tempFile)) {
                fos.write(fileContent);
            }

            // 创建FileItem用于上传
            FileItem fileItem = new DiskFileItemFactory().createItem("file", MediaType.MULTIPART_FORM_DATA_VALUE, true, fileName);
            try (InputStream inputStream = new FileInputStream(tempFile)) {
                IOUtils.copy(inputStream, fileItem.getOutputStream());
            }

            // 创建MultipartFile
            CommonsMultipartFile multipartFile = new CommonsMultipartFile(fileItem);

            // 构建上传URL
            String fileUploadUrl = fileServiceGatewayUrl + "/file/upload";

            // 获取访问令牌
            String accessToken = SessionHelper.getAccessToken();
            if (accessToken == null) {
                throw new RuntimeException("获取token失败");
            }

            // 设置请求头
            Map<String, String> headerMap = new HashMap<>();
            headerMap.put("accesstoken", accessToken);

            // 执行文件上传
            String respStr = HttpRequest.post(fileUploadUrl)
                    .timeout(30000)
                    .addHeaders(headerMap)
                    .form("file", multipartFile)
                    .execute()
                    .body();

            log.info("文件上传响应: {}", respStr);

            // 解析响应
            JSONObject respJson = JSON.parseObject(respStr);
            if (respJson.getInteger("code") == 0) {
                JSONObject result = respJson.getJSONObject("result");
                if (result.containsKey(fileName)) {
                    JSONObject fileResult = result.getJSONObject(fileName);
                    String fileOid = fileResult.getString("oid");

                    log.info("文件上传到Minio成功: {} -> {}", fileName, fileOid);

                    // 清理临时文件
                    tempFile.delete();

                    return fileOid;
                } else {
                    // 如果没有以文件名为key的结果，尝试直接获取oid
                    String fileOid = result.getString("oid");
                    if (fileOid != null) {
                        log.info("文件上传到Minio成功: {} -> {}", fileName, fileOid);
                        tempFile.delete();
                        return fileOid;
                    } else {
                        throw new RuntimeException("文件上传响应中未找到文件OID");
                    }
                }
            } else {
                throw new RuntimeException("文件上传失败: " + respJson.getString("msg"));
            }

        } catch (Exception e) {
            log.error("文件上传失败: {}", fileName, e);
            throw new IOException("文件上传失败: " + e.getMessage(), e);
        }
    }

    /**
     * 构造ECADIntegrationDTO
     */
    private ECADIntegrationDTO buildECADIntegrationDTO(ECADIntegrationDTO meta, Map<String, List<File>> filesByType) {
        ECADIntegrationDTO dto = new ECADIntegrationDTO();

        // 设置基本信息
        dto.setProjectName(meta.getProjectName());
        dto.setEcadNumber(meta.getEcadNumber());
        dto.setEcadName(meta.getEcadName());
        dto.setModelType(meta.getModelType());
        dto.setToolType(meta.getToolType());
        dto.setToolVersion(meta.getToolVersion());
        dto.setCheckin(meta.isCheckin());

        // 1、主文件==项目打包文件.zip(LocalPathOid)   2、生产文件==生产打包文件.zip(ProductInfoPathOid) 3、原理图PDF==原理图PDF.pdf(VisiableOid)

        // 根据文件类型设置对应的文件OID
        // SCHEMATIC - 原理图文件，设置为主文件
        List<File> schematicFiles = filesByType.get("PROJECT");
        if (schematicFiles != null && !schematicFiles.isEmpty()) {
            dto.setLocalPathOid(schematicFiles.get(0).getOid());
        }

        // BOM - BOM文件
        List<File> bomFiles = filesByType.get("BOM");
        if (bomFiles != null && !bomFiles.isEmpty()) {
            dto.setBomDataOid(bomFiles.get(0).getOid());
        }

        // GERBER - Gerber文件，设置为轻量化文件
        List<File> gerberFiles = filesByType.get("GERBER");
        if (gerberFiles != null && !gerberFiles.isEmpty()) {
            dto.setGerberDataOid(gerberFiles.get(0).getOid());
        }

        // STEP - 3D模型文件，设置为产品信息文件
        List<File> stepFiles = filesByType.get("STEP");
        if (stepFiles != null && !stepFiles.isEmpty()) {
            dto.setStepDataOid(stepFiles.get(0).getOid());
        }

        // Schematic_PDF - 原理图文件，设置为包装文件
        List<File> pdfFiles = filesByType.get("Schematic_PDF");
        if (pdfFiles != null && !pdfFiles.isEmpty()) {
            dto.setVisiableOid(pdfFiles.get(0).getOid());
        }

        // Schematic_PDF - 原理图文件，设置为包装文件
        List<File> AssemblyPdfFiles = filesByType.get("Assembly_PDF");
        if (AssemblyPdfFiles != null && !AssemblyPdfFiles.isEmpty()) {
            dto.setAssemblyPDFDataOid(AssemblyPdfFiles.get(0).getOid());
        }

        // ODB++ - ODB++文件，可以作为网表文件
        List<File> odbFiles = filesByType.get("ODB++");
        if (odbFiles != null && !odbFiles.isEmpty()) {
            dto.setODBDataOid(odbFiles.get(0).getOid());
        }

        log.info("构造ECADIntegrationDTO完成: {}", JSON.toJSONString(dto));
        return dto;
    }

}
