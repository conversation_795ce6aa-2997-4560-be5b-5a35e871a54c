package cn.jwis.platform.space.ecad.service.dto;

import lombok.Data;

import java.util.List;

@Data
public class ECADCreateRelParams {

    ECADCreateParams referenceInfo;

    private List<DD> children;

    @Data
    public static class DD {
        private ReferenceInfo referenceInfo;
        private InstanceInfo instanceInfo;
    }

    @Data
    public static class ReferenceInfo {
        private String nodeName;
        private String state;
        private String type;
        private String pdmVersion;
        private String pnumber;
    }

    @Data
    public static class InstanceInfo {
        private String position;
        private String opacity;
        private String rgb;
        private String name;
    }
}

