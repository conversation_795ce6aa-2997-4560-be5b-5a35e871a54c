package cn.jwis.platform.space.ecad.service.impl;

import cn.jwis.framework.base.web.session.SessionHelper;
import cn.jwis.platform.space.ecad.remote.PdmRemote;
import cn.jwis.platform.space.ecad.remote.PdmUserAuthInfo;
import cn.jwis.platform.space.ecad.remote.PdmUserInfo;
import cn.jwis.platform.space.ecad.remote.PdmUserQueryParams;
import cn.jwis.platform.space.ecad.service.interf.UserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 用户服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserServiceImpl implements UserService {
    
    private final PdmRemote pdmRemote;
    
    @Override
    public PdmUserInfo getUserByAccount(String account) {
        log.info("根据账号查询用户信息: {}", account);
        return pdmRemote.getUserByAccount(account);
    }
    
    @Override
    public PdmUserInfo getUserByOid(String userOid) {
        log.info("根据OID查询用户信息: {}", userOid);
        return pdmRemote.getUserByOid(userOid);
    }
    
    @Override
    public PdmUserAuthInfo getUserAuthInfo(PdmUserQueryParams params) {
        log.info("获取用户认证信息: {}", params);
        return pdmRemote.getUserAuthInfo(params);
    }
    
    @Override
    public Boolean validateToken(String accessToken) {
        log.info("验证token有效性");
        return pdmRemote.validateToken(accessToken);
    }
    
    @Override
    public PdmUserAuthInfo refreshToken(String refreshToken) {
        log.info("刷新用户token");
        return pdmRemote.refreshToken(refreshToken);
    }
    
    @Override
    public PdmUserInfo getCurrentUser() {
        try {
            // 从SessionHelper获取当前用户信息
            Object currentUser = SessionHelper.getCurrentUser();
            if (currentUser == null) {
                log.warn("SessionHelper.getCurrentUser() 返回null");
                return null;
            }
            
            // 这里需要根据实际的SessionHelper返回的对象类型进行转换
            // 假设SessionHelper.getCurrentUser()返回的对象有getOid()和getAccount()方法
            String userOid = null;
            String account = null;
            
            // 使用反射获取用户信息
            try {
                userOid = (String) currentUser.getClass().getMethod("getOid").invoke(currentUser);
                account = (String) currentUser.getClass().getMethod("getAccount").invoke(currentUser);
            } catch (Exception e) {
                log.error("获取用户信息失败", e);
                return null;
            }
            
            // 如果有userOid，优先使用userOid查询
            if (userOid != null) {
                return getUserByOid(userOid);
            } else if (account != null) {
                return getUserByAccount(account);
            }
            
            log.warn("无法从SessionHelper获取有效的用户标识");
            return null;
            
        } catch (Exception e) {
            log.error("获取当前用户信息失败", e);
            return null;
        }
    }
    
    @Override
    public String getCurrentUserAccessToken() {
        try {
            // 从SessionHelper获取访问令牌
            String accessToken = SessionHelper.getAccessToken();
            if (accessToken == null) {
                log.warn("SessionHelper.getAccessToken() 返回null");
            }
            return accessToken;
        } catch (Exception e) {
            log.error("获取当前用户访问令牌失败", e);
            return null;
        }
    }
}
