package cn.jwis.platform.space.ecad.service.dto;

public enum SpaceLifecycleEnum {

    ADD(1L, "add"),
    UPDATE(2L, "update"),
    EXPIRE(3L, "expire"),
    NORMAL(4L, "normal");

    private Long code;
    private String status;

    SpaceLifecycleEnum(Long code, String status) {
        this.code = code;
        this.status = status;
    }

    public Long getCode() {
        return code;
    }

    public String getStatus() {
        return status;
    }

    public Long getCodeByStatus(String status) {
        for (SpaceLifecycleEnum value : values()) {
            if (value.getStatus().equalsIgnoreCase(status))
                return value.getCode();
        }
        return null;
    }

    public String getStatusByCode(Long code) {
        for (SpaceLifecycleEnum value : values()) {
            if (value.getCode() == code)
                return value.getStatus();
        }
        return null;
    }

}
