package cn.jwis.platform.space.ecad.service.interf;


import cn.jwis.platform.space.ecad.entity.ECADCatalog;
import cn.jwis.platform.space.ecad.remote.ECADParam;
import cn.jwis.platform.space.ecad.remote.PdmCatalogNode;
import cn.jwis.platform.space.ecad.service.dto.*;

import java.util.List;

public interface ECADCatalogService {
    ECADCatalogDTO create(ECADCatalogCreateParams params);

    List<ECADCatalogNode> tree();

    void delete(String oid);

    void clear(String oid);

    void activate(String oid);

    List<PdmCatalogNode> pdmTree();

    ECADCatalog activeWorkspace();
}
