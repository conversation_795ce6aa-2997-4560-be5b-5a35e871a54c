package cn.jwis.platform.space.ecad.service.dto;

import cn.jwis.platform.space.ecad.entity.ECADFile;
import cn.jwis.platform.space.ecad.entity.ECADIteration;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class ECADDTO extends ECADIteration {

    @ApiModelProperty("文件最后修改时间")
    private Date fileLastModified;

    @ApiModelProperty("是否pdm最新版本")
    private boolean pdmLatest;

    @ApiModelProperty("主文件")
    private List<ECADFile> primaryFiles;

    @ApiModelProperty("附件")
    private List<ECADFile> secondaryFiles;

    @ApiModelProperty("bom结构")
    private BomData bomData;

    @ApiModelProperty("是否需要检入")
    private boolean needCheckin = true;

    @ApiModelProperty("无需检入的描述信息")
    private String checkinMessage;
}
