package cn.jwis.platform.space.ecad.service.interf;

import cn.jwis.platform.space.ecad.remote.PdmUserAuthInfo;
import cn.jwis.platform.space.ecad.remote.PdmUserInfo;
import cn.jwis.platform.space.ecad.remote.PdmUserQueryParams;

/**
 * 用户服务接口
 */
public interface UserService {
    
    /**
     * 根据用户账号查询用户信息
     * @param account 用户账号
     * @return 用户信息
     */
    PdmUserInfo getUserByAccount(String account);
    
    /**
     * 根据用户OID查询用户信息
     * @param userOid 用户OID
     * @return 用户信息
     */
    PdmUserInfo getUserByOid(String userOid);
    
    /**
     * 获取用户认证信息(包含token)
     * @param params 查询参数
     * @return 用户认证信息
     */
    PdmUserAuthInfo getUserAuthInfo(PdmUserQueryParams params);
    
    /**
     * 验证用户token有效性
     * @param accessToken 访问令牌
     * @return 是否有效
     */
    Boolean validateToken(String accessToken);
    
    /**
     * 刷新用户token
     * @param refreshToken 刷新令牌
     * @return 新的认证信息
     */
    PdmUserAuthInfo refreshToken(String refreshToken);
    
    /**
     * 获取当前用户信息(从SessionHelper获取)
     * @return 当前用户信息
     */
    PdmUserInfo getCurrentUser();
    
    /**
     * 获取当前用户的访问令牌(从SessionHelper获取)
     * @return 访问令牌
     */
    String getCurrentUserAccessToken();
}
