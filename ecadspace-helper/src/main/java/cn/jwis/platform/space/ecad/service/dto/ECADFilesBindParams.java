package cn.jwis.platform.space.ecad.service.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class ECADFilesBindParams {

    private String nodeName;

    private boolean dsVersionModified;

    private List<String> projName = new ArrayList<>();

    private String pdmVersion;

    @ApiModelProperty("是否主文件")
    private boolean primary;

    private String fileOid;

    private String fileName;

    private String fileType;

    private String filePath;

    @ApiModelProperty(value = "文件修改时间", example = "2022-05-20 06:04:50")
    private String LastModified;
}
