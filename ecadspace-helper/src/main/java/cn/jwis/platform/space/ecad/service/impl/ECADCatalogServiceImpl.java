package cn.jwis.platform.space.ecad.service.impl;

import cn.jwis.framework.base.bean.util.BeanUtil;
import cn.jwis.framework.base.exception.JWIServiceException;
import cn.jwis.framework.base.util.OidGenerator;
import cn.jwis.framework.base.web.session.SessionHelper;
import cn.jwis.platform.space.ecad.domain.interf.ECADCatalogDomainService;
import cn.jwis.platform.space.ecad.domain.interf.ECADDomainService;
import cn.jwis.platform.space.ecad.domain.interf.ECADIterationDomainService;
import cn.jwis.platform.space.ecad.entity.ECAD;
import cn.jwis.platform.space.ecad.entity.ECADCatalog;
import cn.jwis.platform.space.ecad.entity.ECADIteration;
import cn.jwis.platform.space.ecad.remote.PdmCatalogNode;
import cn.jwis.platform.space.ecad.remote.PdmRemote;
import cn.jwis.platform.space.ecad.service.dto.ECADCatalogCreateParams;
import cn.jwis.platform.space.ecad.service.dto.ECADCatalogDTO;
import cn.jwis.platform.space.ecad.service.dto.ECADCatalogNode;
import cn.jwis.platform.space.ecad.service.dto.ECADDTO;
import cn.jwis.platform.space.ecad.service.interf.ECADCatalogService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;

import static java.util.stream.Collectors.toList;

@Service
@RequiredArgsConstructor
public class ECADCatalogServiceImpl implements ECADCatalogService {

    private final ECADCatalogDomainService ecadCatalogDomainService;
    private final ECADDomainService ecadDomainService;
    private final ECADIterationDomainService ecadIterationDomainService;
    private final PdmRemote pdmRemote;

    @Override
    public ECADCatalogDTO create(ECADCatalogCreateParams params) {
        String userOid = SessionHelper.getCurrentUser().getOid();
        String tenantOid = SessionHelper.getCurrentUser().getTenantOid();
        List<ECADCatalog> ecadCatalogs = ecadCatalogDomainService.queryTopECADCatalogByUserOid(userOid);

        Optional<ECADCatalog> optional = ecadCatalogs.stream()
                .filter(c -> c.getName().equals(params.getName()) && c.getTenantOid().equals(tenantOid))
                .findFirst();

        if (optional.isPresent()) {
            throw new JWIServiceException("已存在同名工作区");
        }

        ECADCatalog ecadCatalog = new ECADCatalog();
        ecadCatalog.setOid(OidGenerator.newOid());
        ecadCatalog.setName(params.getName());
        ecadCatalog.setUserOid(userOid);
        ecadCatalog.setTop(true);
        ecadCatalog.setActive(false);
        ecadCatalog.setContainerOid(params.getContainerOid());
        ecadCatalog.setContainerType(params.getContainerType());
        ecadCatalog.setPdmDefaultFullPaths(params.getPdmDefaultFullPaths());
        ecadCatalog.setContainerModel(params.getContainerModel());
        ecadCatalog.setSpaceContainerOid(OidGenerator.newOid());

        ecadCatalogDomainService.create(ecadCatalog);
        return BeanUtil.clone(ecadCatalog, ECADCatalogDTO.class);
    }

    @Override
    public List<ECADCatalogNode> tree() {
        String userOid = SessionHelper.getCurrentUser().getOid();
        String tenantOid = SessionHelper.getCurrentUser().getTenantOid();
        List<ECADCatalog> topECADCatalogs = ecadCatalogDomainService.queryTopECADCatalogByUserOid(userOid);

        topECADCatalogs = topECADCatalogs.stream().filter(c -> c.getTenantOid().equals(tenantOid)).collect(toList());

        if (topECADCatalogs.isEmpty()) {
            return Collections.emptyList();
        }
        return BeanUtil.cloneList(topECADCatalogs, ECADCatalogNode.class).stream()
                .sorted(Comparator.comparing(ECADCatalogNode::isActive, Comparator.reverseOrder()).thenComparing(ECADCatalogNode::getUpdateDate))
                .collect(toList());
    }


    @Override
    public void delete(String oid) {
        removeContainedECADs(oid);
        ecadCatalogDomainService.deleteByOid(oid);
    }

    @Override
    public void clear(String oid) {
        removeContainedECADs(oid);
    }

    @Override
    public void activate(String oid) {
        String userOid = SessionHelper.getCurrentUser().getOid();
        List<ECADCatalog> ecadCatalogs = ecadCatalogDomainService.queryTopECADCatalogByUserOid(userOid);

        for (ECADCatalog ecadCatalog : ecadCatalogs) {
            if (ecadCatalog.getOid().equals(oid)) {
                ecadCatalog.setActive(true);
                ecadCatalogDomainService.updateByOid(ecadCatalog);
            } else if (ecadCatalog.isActive()) {
                ecadCatalog.setActive(false);
                ecadCatalogDomainService.updateByOid(ecadCatalog);
            }
        }
    }

    @Override
    public List<PdmCatalogNode> pdmTree() {
        ECADCatalog ecadCatalog = activeWorkspace();
        return pdmRemote.searchTree(ecadCatalog.getContainerOid(), ecadCatalog.getContainerModel());
    }

    @Override
    public ECADCatalog activeWorkspace() {
        String userOid = SessionHelper.getCurrentUser().getOid();
        List<ECADCatalog> ecadCatalogs = ecadCatalogDomainService.queryTopECADCatalogByUserOid(userOid);
        Optional<ECADCatalog> optional = ecadCatalogs.stream().filter(ECADCatalog::isActive).findFirst();
        if (!optional.isPresent()) {
            throw new JWIServiceException("当前暂无激活工作区");
        }

        return optional.get();
    }

    private void removeContainedECADs(String parentOid) {
        List<ECAD> ecads = ecadCatalogDomainService.queryECADByParentOid(parentOid);

        List<String> ecadOids = ecads.stream().map(ECAD::getOid).collect(toList());
        List<ECADIteration> ecadIterations = ecadIterationDomainService.queryLatestIterationBatch(ecadOids);

        ecads.forEach(c -> ecadDomainService.deleteByOid(c.getOid()));
        ecadIterations.forEach(c -> ecadIterationDomainService.deleteByOid(c.getOid()));
    }

    private void dealPdmLatest(ECADDTO ecaddto) {
        if (ecaddto.getPdmVersion() == null) {
            ecaddto.setPdmLatest(true);
        } else if (ecaddto.getPdmVersion().equals(ecaddto.getPdmLatestVersion())) {
            ecaddto.setPdmLatest(true);
        }
    }

}
