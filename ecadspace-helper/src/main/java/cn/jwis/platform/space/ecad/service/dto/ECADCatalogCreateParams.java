package cn.jwis.platform.space.ecad.service.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class ECADCatalogCreateParams {

    @ApiModelProperty("文件夹名称")
    private String name;

    @ApiModelProperty("父级文件夹oid, 不传默认为顶级")
    private String parentOid;

    @ApiModelProperty("容器oid")
    private String containerOid;

    @ApiModelProperty("容器oid")
    private String containerType;

    @ApiModelProperty("pdm文件夹全路径路径")
    private List<String> pdmDefaultFullPaths = new ArrayList<>();

    @ApiModelProperty(value = "容器类型", allowableValues = "ProductContainer,ResourceContainer")
    private String containerModel;
}
