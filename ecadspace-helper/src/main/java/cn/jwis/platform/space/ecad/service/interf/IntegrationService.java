package cn.jwis.platform.space.ecad.service.interf;

import cn.jwis.platform.space.ecad.entity.ECADCatalog;
import cn.jwis.platform.space.ecad.entity.ECADIteration;
import cn.jwis.platform.space.ecad.remote.ECADIntegrationDTO;
import cn.jwis.platform.space.ecad.remote.PdmCADInfo;
import cn.jwis.platform.space.ecad.service.dto.ECADFileDTO;
import cn.jwis.platform.space.ecad.service.dto.SyncParams;
import cn.jwis.platform.space.ecad.service.dto.SyncResDTO;
import com.alibaba.fastjson.JSONObject;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

public interface IntegrationService {
    SyncResDTO sync(SyncParams params);

    List<ECADFileDTO> downloadFiles(List<String> allPmdECADIntegrationOids, String userOid, ECADCatalog ecadCatalog);

    ECADIteration buildECADIteration(PdmCADInfo cadInfo, String userOid, String spaceContainerOid);

    /**
     * EDM文件压缩包接入并检入space
     * @param metaJson 元数据JSON字符串
     * @param zipFile 压缩包文件
     * @return 检入结果
     */
    JSONObject edmIntegrationCheckin(String metaJson, MultipartFile zipFile);
}
