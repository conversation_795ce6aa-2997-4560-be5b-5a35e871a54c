package cn.jwis.platform.space.ecad.service.interf;

import cn.jwis.platform.space.ecad.entity.ECADCatalog;
import cn.jwis.platform.space.ecad.entity.ECADIteration;
import cn.jwis.platform.space.ecad.remote.PdmCADInfo;
import cn.jwis.platform.space.ecad.service.dto.ECADFileDTO;
import cn.jwis.platform.space.ecad.service.dto.SyncParams;
import cn.jwis.platform.space.ecad.service.dto.SyncResDTO;

import java.util.List;

public interface IntegrationService {
    SyncResDTO sync(SyncParams params);

    List<ECADFileDTO> downloadFiles(List<String> allPmdECADIntegrationOids, String userOid, ECADCatalog ecadCatalog);

    ECADIteration buildECADIteration(PdmCADInfo cadInfo, String userOid, String spaceContainerOid);
}
