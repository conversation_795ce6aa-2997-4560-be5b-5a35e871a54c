package cn.jwis.platform.space.ecad.service.interf;


import cn.jwis.framework.base.response.PageResult;
import cn.jwis.platform.space.ecad.entity.ECADCatalog;
import cn.jwis.platform.space.ecad.remote.ECADIntegrationDTO;
import cn.jwis.platform.space.ecad.service.dto.*;
import com.alibaba.fastjson.JSONObject;

import java.util.List;

public interface ECADService {
    List<ECADDTO> histories(String oid);

    List<ECADFileDTO> download(String iterationOid);

    void checkout(String oid);

    void cancelCheckOut(String oid);

    List<ECADDTO> checkinPreview(List<String> oids);

    void checkinConfirm(CheckinConfirmParams params);

    PageResult<ECADDTO> getList(Integer page, Integer size, String ecadCatalogOid, String name);

    JSONObject saveAndCheckin(ECADIntegrationDTO ecadIntegrationDTO);

    List<ECADDTODetail> queryDetail(String oid);

    void syncStatus(List<String> oids);

    Object initOrUpdateLibrary(String path);

    Object searchCatalogTree();

    Object searchSchematic();

    Object syncToolData(Object object);
}
