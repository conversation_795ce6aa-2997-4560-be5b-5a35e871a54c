package cn.jwis.platform.space.ecad.service.impl;

import cn.jwis.framework.base.bean.util.BeanUtil;
import cn.jwis.framework.base.domain.able.info.ModelInfo;
import cn.jwis.framework.base.exception.JWIException;
import cn.jwis.framework.base.exception.JWIServiceException;
import cn.jwis.framework.base.response.PageResult;
import cn.jwis.framework.base.util.ExcelUtils;
import cn.jwis.framework.base.util.OidGenerator;
import cn.jwis.framework.base.util.StringUtil;
import cn.jwis.framework.base.web.session.SessionHelper;
import cn.jwis.platform.space.ecad.ECADAndECADIntegration;
import cn.jwis.platform.space.ecad.domain.interf.ECADCatalogDomainService;
import cn.jwis.platform.space.ecad.domain.interf.ECADDomainService;
import cn.jwis.platform.space.ecad.domain.interf.ECADFileDomainService;
import cn.jwis.platform.space.ecad.domain.interf.ECADIterationDomainService;
import cn.jwis.platform.space.ecad.entity.ECAD;
import cn.jwis.platform.space.ecad.entity.ECADCatalog;
import cn.jwis.platform.space.ecad.entity.ECADIteration;
import cn.jwis.platform.space.ecad.entity.File;
import cn.jwis.platform.space.ecad.remote.*;
import cn.jwis.platform.space.ecad.service.dto.*;
import cn.jwis.platform.space.ecad.service.interf.ECADCatalogService;
import cn.jwis.platform.space.ecad.service.interf.ECADService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.io.ByteArrayInputStream;
import java.nio.charset.Charset;
import java.util.*;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toList;

@Slf4j
@Service
@RequiredArgsConstructor
public class ECADServiceImpl implements ECADService {

    private final ECADDomainService ecadDomainService;
    private final ECADIterationDomainService ecadIterationDomainService;
    private final ECADCatalogDomainService ecadCatalogDomainService;
    private final ECADFileDomainService ecadFileDomainService;
    private final PdmRemote pdmRemote;
    private final ECADCatalogService ecadCatalogService;

    @Value("${file.service.gateway.url}")
    private String FILE_SERVICE_URL;

    private final RestTemplate restTemplate = new RestTemplate();

    private ECADCatalog getActiveCatalog(String userOid) {
        List<ECADCatalog> ecadCatalogs = ecadCatalogDomainService.queryTopECADCatalogByUserOid(userOid);
        Optional<ECADCatalog> optional = ecadCatalogs.stream().filter(ECADCatalog::isActive).findFirst();
        if (!optional.isPresent()) {
            throw new JWIServiceException("当前暂无激活工作区");
        }
        return optional.get();
    }

    @Override
    public List<ECADDTODetail> queryDetail(String oid) {
        ECAD ecad = ecadDomainService.queryECADByIterationOid(oid);
        List<ECADIteration> ecadIterations = ecadDomainService.queryAllIterationByECADOid(ecad.getOid());
        return ecadIterations.stream().map(ecadIteration -> {
            ECADDTODetail ecaddtoDetail = new ECADDTODetail();
            ECADDTO clone = BeanUtil.clone(ecadIteration, ECADDTO.class);
            BomData bomData = new BomData(ecadIteration.getProjectName(), ecadIteration.getNumber(), downloadBomData(ecadIteration.getBomDataOid(), ecadIteration.getToolType()));
            clone.setBomData(bomData);

            ecaddtoDetail.setKey(clone.getDisplayVersion());
            ecaddtoDetail.setValue(clone);
            return ecaddtoDetail;
        }).collect(toList());
    }

    @Override
    public void syncStatus(List<String> oids) {
        if (oids.isEmpty()) {
            List<ECADIteration> allActiveMcadIterations = getAllActiveEcadIterations();
            oids = allActiveMcadIterations.stream().map(ECADIteration::getOid).collect(toList());
        }
        if (oids.isEmpty()) {
            return;
        }

        List<ECADIteration> mcadIterations = ecadIterationDomainService.batchQueryECADIterationsByOids(oids);
        List<String> numbers = mcadIterations.stream().map(ECADIteration::getPnumber).collect(toList());
        List<PdmCADInfo> pdmCADInfos = pdmRemote.getCadInfoByNumbers(numbers, false, false);

        String userOid = SessionHelper.getCurrentUser().getOid();
        ECADCatalog catalog = ecadIterationDomainService.queryCatalogByEcadIterationOid(oids.get(0));

        List<String> pdmCadNames = mcadIterations.stream().map(ECADIteration::getName).collect(toList());
        Map<String, ECADAndECADIntegration> aggregateMap = ecadDomainService.queryECADAndECADIterationByNames(userOid, pdmCadNames, catalog.getSpaceContainerOid());

        List<ECADIteration> readyMCADIteration = new ArrayList<>();
        syncStatusData(pdmCADInfos, aggregateMap, readyMCADIteration);
        ecadIterationDomainService.batchUpdateECADIteration(readyMCADIteration);
    }

    private void syncStatusData(List<PdmCADInfo> pdmCADInfos, Map<String, ECADAndECADIntegration> aggregateMap, List<ECADIteration> readyECADIteration) {
        if (pdmCADInfos == null) {
            return;
        }

        for (PdmCADInfo cadInfo : pdmCADInfos) {
            ECADAndECADIntegration ecadAndECADIntegration = aggregateMap.get(cadInfo.getName());
            if (ecadAndECADIntegration != null) {
                ECADIteration ecadIteration = ecadAndECADIntegration.getEcadIteration();
                if (StringUtil.isNotBlank(cadInfo.getLockOwnerAccount())) {
                    ecadIteration.setLockNote(cadInfo.getLockNote());
                    ecadIteration.setLockOwnerAccount(cadInfo.getLockOwnerAccount());
                    ecadIteration.setLockOwnerOid(cadInfo.getLockOwnerOid());
                    ecadIteration.setLockedTime(cadInfo.getLockedTime());
                    ecadIteration.setLockSourceOid(cadInfo.getOid());

                    ecadIteration.setSourceOid(cadInfo.getLockSourceOid());
                } else {
                    ecadIteration.setLockNote(null);
                    ecadIteration.setLockOwnerAccount(null);
                    ecadIteration.setLockOwnerOid(null);
                    ecadIteration.setLockedTime(null);
                    ecadIteration.setLockSourceOid(null);
                }

                ecadIteration.setLifecycleOid(cadInfo.getLifecycleOid());
                ecadIteration.setLifecycleStatus(cadInfo.getLifecycleStatus());
                String pdmDisplayVersion = cadInfo.getDisplayVersion().replace("(Working Copy)", "");
                if (!pdmDisplayVersion.equals(ecadIteration.getPdmVersion().replace("(Working Copy)", ""))) {
//                    mcadIteration.setSourceOid(cadInfo.getOid());
                    ecadIteration.setPdmLatestVersion(pdmDisplayVersion);
//                    mcadIteration.setPdmVersion(cadInfo.getDisplayVersion());
//                    mcadIteration.setPdmCatalogFullPaths(cadInfo.getFolderPath().stream().map(PdmCatalog::getName).collect(toList()));
//                    mcadIteration.setIteratedVersion(mcadIteration.getIteratedVersion() + 1);
//                    mcadIteration.setDisplayVersion("V." + mcadIteration.getIteratedVersion());
                    ecadIteration.setSpaceLifecycleCode(SpaceLifecycleEnum.EXPIRE.getCode());
                    ecadIteration.setSpaceLifecycleStatus(SpaceLifecycleEnum.EXPIRE.getStatus());
                }
                if (!readyECADIteration.contains(ecadIteration)) {
                    readyECADIteration.add(ecadIteration);
                }
            }
        }
    }

    private List<ECADIteration> getAllActiveEcadIterations() {
        ECADCatalog catalog = getActiveCatalog(SessionHelper.getCurrentUser().getOid());
        List<ECAD> mcads = ecadCatalogDomainService.queryECADByParentOid(catalog.getOid());
        List<String> mcadOids = mcads.stream().map(ECAD::getOid).collect(toList());

        return ecadIterationDomainService.queryLatestIterationBatch(mcadOids);
    }

    private Object downloadBomData(String oid, String toolType) {
        if (org.springframework.util.StringUtils.isEmpty(oid)) {
            return new ArrayList<>();
        }
        HttpHeaders headers = new HttpHeaders();
        HttpEntity httpEntity = new HttpEntity<>(headers);
        String url = String.format("%s/file/downloadByOid?fileOid=%s", FILE_SERVICE_URL, oid);
        String bomJson = null;
        try {
            ResponseEntity<byte[]> responseEntity = restTemplate.exchange(url, HttpMethod.GET, httpEntity, byte[].class);
            byte[] bytes = responseEntity.getBody();
            if (bytes == null) {
                throw new JWIServiceException("获取远程文件失败【 " + oid + "】");
            }

            List<String> fileNames = responseEntity.getHeaders().getOrEmpty("Content-Disposition");
            if (!fileNames.isEmpty() && (fileNames.get(0).endsWith(".xlsx") || fileNames.get(0).endsWith(".xls"))) {
                if ("AltiumDesigner".equalsIgnoreCase(toolType) ||
                        "MentorPadsLogic".equalsIgnoreCase(toolType) ||
                        "MentorPadsLayout".equalsIgnoreCase(toolType) ||
                        "Pads".equalsIgnoreCase(toolType)) {

                    // 读取Excel数据
                    List<ADBomFileData> bomFileData = ExcelUtils.read(new ByteArrayInputStream(bytes), ADBomFileData.class);

                    if (bomFileData != null) {
                        // 过滤掉无效数据
                        bomFileData = bomFileData.stream()
                                .filter(b -> b != null && b.getPartnumber() != null)
                                .collect(Collectors.toList());

                        // 按 "编码" 和 "名称" 分组，合并数据
                        Map<String, ADBomFileData> mergedDataMap = bomFileData.stream()
                                .collect(Collectors.toMap(
                                        // 分组的key：编码 + 名称
                                        b -> b.getPartnumber() + "_" + b.getComment(),
                                        // 初始值
                                        b -> {
                                            ADBomFileData newData = new ADBomFileData();
                                            newData.setPartnumber(b.getPartnumber());
                                            newData.setComment(b.getComment());
                                            newData.setQuantity(b.getQuantity());
                                            newData.setPosition(b.getPosition());
                                            return newData;
                                        },
                                        // 合并逻辑
                                        (b1, b2) -> {
                                            // 数量累加
                                            int quantity1 = Integer.parseInt(b1.getQuantity());
                                            int quantity2 = Integer.parseInt(b2.getQuantity());
                                            b1.setQuantity(String.valueOf(quantity1 + quantity2));

                                            // 位号拼接
                                            b1.setPosition(b1.getPosition() + "," + b2.getPosition());
                                            return b1;
                                        }
                                ));

                        // 转换为列表
                        List<ADBomFileData> mergedBomFileData = new ArrayList<>(mergedDataMap.values());

                        // 打印合并后的结果
                        log.info("AltiumDesigner下当前bomFileData:{}", JSON.toJSONString(mergedBomFileData));

                        // 构造 JSON 对象
                        JSONObject jsonObject = new JSONObject();
                        jsonObject.put("list", mergedBomFileData);
                        bomJson = JSONObject.toJSONString(jsonObject);
                    }
                } else {
                    List<BomFileData> bomFileData = ExcelUtils.read(new ByteArrayInputStream(bytes), BomFileData.class);
                    if (bomFileData != null) {
                        bomFileData = bomFileData.stream().filter(b -> b != null && b.getPartnumber() != null).collect(toList());
                    }

                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("list", bomFileData);
                    bomJson = JSONObject.toJSONString(jsonObject);
                }

            } else if ("Allegro".equalsIgnoreCase(toolType)) {
                List<BomFileData> bomFileData = handlerAllegro(bytes);
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("list", bomFileData);
                bomJson = JSONObject.toJSONString(jsonObject);
            } else {
                bomJson = new String(bytes, Charset.forName("GBK"));
            }

        } catch (Exception e) {
            e.printStackTrace();
            throw new JWIServiceException("获取远程文件失败【 " + oid + "】");
        }

        try {
            return JSONObject.parseObject(bomJson);
        } catch (Exception e) {
            e.printStackTrace();
            throw new JWIServiceException("JSON解析失败【" + bomJson + "】");
        }

    }

    private List<BomFileData> handlerAllegro(byte[] bytes) {
        List<BomFileData> res = new ArrayList<>();
        String bomData = new String(bytes, Charset.forName("GBK"));
        String[] lines = bomData.split("\r\n|\r|\n");
        int start = 0;
        for (int i = 0; i < lines.length; i++) {
            String line = lines[i];
            if (StringUtil.isBlank(line)) {
                start = i + 2;
            }
            if (start != 0 && i >= start && StringUtil.isNotBlank(line)) {
                String[] bomLine = line.split(";");
                try {
                    if(Objects.equals(bomLine[0], "TOTAL")){
                        continue;
                    }
                    BomFileData bomFileData = new BomFileData();
                    bomFileData.setPartnumber(bomLine[0].replace("?", ""));
                    bomFileData.setPartname(bomLine[1].replace("?", ""));
                    bomFileData.setQuantity(Objects.isNull(bomLine[2]) ? 1 : Integer.parseInt(bomLine[2]));
                    bomFileData.setPartreference(bomLine[3].replace("?", ""));
                    res.add(bomFileData);
                } catch (NumberFormatException e) {
                    throw new JWIException("bom列表excel解析失败列名称顺序：编码，名称，数量，位号。");
                }
            }
        }
        return res;
    }


    @Override
    public JSONObject saveAndCheckin(ECADIntegrationDTO integrationDTO) {
        System.out.println("integrationDTO: " + JSONObject.toJSONString(integrationDTO));

        ECADIteration ecadIteration = save(integrationDTO);

        if (integrationDTO.isCheckin()) {
            integrationDTO.setEcadNumber(ecadIteration.getSourceOid() == null ? "" : ecadIteration.getSourceOid());
            PdmECADIteration pdmECADIteration = pdmRemote.ecadCheckin(integrationDTO);
            writeBackPdmECADData(pdmECADIteration, ecadIteration);
        }
        return JSONObject.parseObject(JSONObject.toJSONString(ecadIteration));
    }

    private void writeBackPdmECADData(PdmECADIteration pdmECADIteration, ECADIteration ecadIteration) {
        ecadIteration.setLifecycleOid(pdmECADIteration.getLifecycleOid());
        ecadIteration.setLifecycleStatus(pdmECADIteration.getLifecycleStatus());
        ecadIteration.setPdmVersion(pdmECADIteration.getDisplayVersion());
        ecadIteration.setPdmLatestVersion(pdmECADIteration.getDisplayVersion());
        ecadIteration.setContainerOid(pdmECADIteration.getContainerOid());
        ecadIteration.setContainerType(pdmECADIteration.getContainerType());
        ecadIteration.setPnumber(pdmECADIteration.getNumber());
        ecadIteration.setNumber(pdmECADIteration.getNumber());
        ecadIteration.setEcadNumber(pdmECADIteration.getNumber());

        ecadIteration.setSourceOid(pdmECADIteration.getOid());
        ecadIteration.setModelDefinition(pdmECADIteration.getModelDefinition());

        ecadIteration.setLockNote(null);
        ecadIteration.setLockOwnerAccount(null);
        ecadIteration.setLockOwnerOid(null);
        ecadIteration.setLockedTime(null);
        ecadIteration.setLockSourceOid(null);

        ecadIteration.setCheckin(true);

        ecadIteration.setSpaceLifecycleCode(SpaceLifecycleEnum.NORMAL.getCode());
        ecadIteration.setSpaceLifecycleStatus(SpaceLifecycleEnum.NORMAL.getStatus());

//        List<PdmCatalog> pdmCatalogs = pdmRemote.findAllFolderByOid(pdmECADIteration.getCatalogOid());
//        ecadIteration.setPdmCatalogFullPaths(pdmCatalogs.stream().map(PdmCatalog::getName).collect(toList()));

        ecadIterationDomainService.updateLockByOid(ecadIteration);
    }

    private ECADIteration save(ECADIntegrationDTO integrationDTO) {
        String userOid = SessionHelper.getCurrentUser().getOid();

        ECADCatalog innerEcadCatalog = null;
        if ("Allegro".equalsIgnoreCase(integrationDTO.getToolType())) {
            innerEcadCatalog = getActiveCatalog(userOid);
        } else {
            if (StringUtils.isNotBlank(integrationDTO.getSchematicNumber())) {
                ECADIteration ecadIteration = ecadIterationDomainService.queryECADIterationBySourceOid(userOid, integrationDTO.getSchematicNumber());
                innerEcadCatalog = ecadIterationDomainService.queryCatalogByEcadIterationOid(ecadIteration.getOid());
            } else {
                innerEcadCatalog = getActiveCatalog(userOid);
            }
        }

        long currentIterationVersion = 1L;
        ECAD dbEcad = ecadDomainService.queryECADByName(userOid, integrationDTO.getProjectName(), innerEcadCatalog.getSpaceContainerOid());
        ECADIteration ecadIteration = null;
        if (dbEcad == null) {
            dbEcad = createECAD(userOid, integrationDTO.getProjectName(), innerEcadCatalog);
            ecadCatalogDomainService.createContainECADRelationship(innerEcadCatalog.getOid(), dbEcad.getOid());
            ecadIteration = buildECADIteration(integrationDTO, userOid, currentIterationVersion, innerEcadCatalog);

            ECADIteration newECADIteration = ecadIterationDomainService.create(ecadIteration);
            ecadDomainService.createIterateRelationship(dbEcad.getOid(), newECADIteration.getOid());
            return newECADIteration;
        }

        ECADIteration oldIteration = ecadIterationDomainService.queryLatestIterationsByECADOid(dbEcad.getOid());
        if (!integrationDTO.getModelType().equals(oldIteration.getModelType())) {
            String modelType = oldIteration.getModelType();
            modelType = "PCB".equalsIgnoreCase(modelType) ? "PCB" : "原理图";
            throw new JWIServiceException(String.format("已存在%s%s，请重命名", modelType, integrationDTO.getProjectName()));
        }

        currentIterationVersion = oldIteration.getIteratedVersion() + 1;
        ecadIterationDomainService.updateLatestIsFalseByOid(oldIteration.getOid());
        ecadIterationDomainService.updateCheckInByOid(oldIteration.getOid(), false);

        List<ECAD> ecads = ecadCatalogDomainService.queryECADByParentOid(innerEcadCatalog.getOid());
        String dbEcadOid = dbEcad.getOid();
        if (ecads.stream().noneMatch(ecad -> ecad.getOid().equals(dbEcadOid))) {
            ecadCatalogDomainService.createContainECADRelationship(innerEcadCatalog.getOid(), dbEcadOid);
        }
        ecadIteration = buildECADIteration(integrationDTO, userOid, currentIterationVersion, innerEcadCatalog, oldIteration);

        ECADIteration newECADIteration = ecadIterationDomainService.create(ecadIteration);
        ecadDomainService.createIterateRelationship(dbEcadOid, newECADIteration.getOid());
        return newECADIteration;
    }

    private String getFirstPath(ECADIntegrationDTO integrationDTO) {
        String productPath = integrationDTO.getProductPath();
        String trim = trim(productPath);
        String[] split = trim.split("\\\\");
        return split[0];
    }

    private ECADCatalog getCurrentECADCatalog(String path, String userOid) {
        List<ECADCatalog> topEcadCatalogs = ecadCatalogDomainService.queryTopECADCatalogByUserOid(userOid);
        Optional<ECADCatalog> optional = topEcadCatalogs.stream()
                .filter(c -> c.getName().equals(path))
                .findFirst();

        return optional.orElseGet(() -> createECADCatalog(path));
    }

    private ECADCatalog createECADCatalog(String path) {
        List<PdmContainer> pdmContainers = pdmRemote.queryAllPdmContainer();
        Optional<PdmContainer> optional = pdmContainers.stream().filter(pc -> pc.getName().equals(path)).findFirst();

        if (!optional.isPresent()) {
            throw new JWIServiceException("PDM无此产品库[" + path + "]");
        }

        PdmContainer pdmContainer = optional.get();

        ECADCatalogCreateParams params = new ECADCatalogCreateParams();
        params.setName(pdmContainer.getName());
        params.setContainerOid(pdmContainer.getOid());
        params.setContainerModel(pdmContainer.getModelDefinition());
        ECADCatalogDTO ecadCatalogDTO = ecadCatalogService.create(params);

        return BeanUtil.clone(ecadCatalogDTO, ECADCatalog.class);
    }

    // 去掉字符串头部的 "\\"
    private String trim(String str) {
        if (str.startsWith("\\"))
            return trim(str.replaceFirst("\\\\", ""));
        return str;
    }

    @Override
    public PageResult<ECADDTO> getList(Integer page, Integer size, String ecadCatalogOid, String name) {
        List<ECADIteration> ecadIterations = new ArrayList<>();

        fullList(ecadIterations, ecadCatalogOid);

        List<ECADDTO> list = BeanUtil.cloneList(ecadIterations, ECADDTO.class).stream().peek(this::dealPdmLatest).collect(toList());

        List<ECADDTO> collect = list.stream().skip((long) (page - 1) * size).limit(size).collect(toList());
        return new PageResult<>(list.size(), page, size, collect);
    }

    private void fullList(List<ECADIteration> list, String ecadCatalogOid) {
        List<ECAD> ecads = ecadCatalogDomainService.queryECADByParentOid(ecadCatalogOid);
        if (!ecads.isEmpty()) {
            List<String> ecadOids = ecads.stream().map(ECAD::getOid).collect(toList());
            List<ECADIteration> ecadIterations = ecadIterationDomainService.queryLatestIterationBatch(ecadOids);

            list.addAll(ecadIterations);
        }

        List<ECADCatalog> ecadCatalogs = ecadCatalogDomainService.queryByParentOid(ecadCatalogOid);
        for (ECADCatalog ecadCatalog : ecadCatalogs) {
            fullList(list, ecadCatalog.getOid());
        }
    }

    private void dealPdmLatest(ECADDTO ecaddto) {
        if (ecaddto.getPdmVersion() == null) {
            ecaddto.setPdmLatest(true);
        } else if (ecaddto.getPdmVersion().equals(ecaddto.getPdmLatestVersion())) {
            ecaddto.setPdmLatest(true);
        }
    }

    @Override
    public List<ECADDTO> checkinPreview(List<String> oids) {
        String account = SessionHelper.getCurrentUser().getAccount();
        if (oids.isEmpty()) {
            List<ECADIteration> allActiveMcadIterations = getAllActiveMcadIterations();
            oids = allActiveMcadIterations.stream().filter(i -> !i.isCheckin()).map(ECADIteration::getOid).collect(toList());
        }

        List<ECADIteration> ecadIterations = ecadIterationDomainService.batchQueryECADIterationsByOids(oids);

        return BeanUtil.cloneList(ecadIterations, ECADDTO.class).stream()
                .collect(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(ECADIteration::getOid)))).stream()
                .sorted(Comparator.comparing((ECADDTO::isCheckin)))
                .peek(iteration -> {
                    List<String> checkinMessages = new ArrayList<>();
                    if (iteration.isCheckin()) {
                        iteration.setNeedCheckin(false);
                        checkinMessages.add("已检入");
                    }

                    if (StringUtils.isNotBlank(iteration.getLockOwnerAccount()) && !iteration.getLockOwnerAccount().equals(account)) {
                        iteration.setNeedCheckin(false);
                        checkinMessages.add("已被[" + iteration.getLockOwnerAccount() + "]检出");
                    }

                    if (iteration.getPdmVersion() != null && !iteration.getPdmVersion().equals(iteration.getPdmLatestVersion())) {
                        iteration.setNeedCheckin(false);
                        checkinMessages.add("当前pdm版本不是最新的");
                    }

                    if (!iteration.isNeedCheckin()) {
                        iteration.setCheckinMessage(checkinMessages.toString().substring(1, checkinMessages.toString().length() - 1));
                    }
                }).peek(this::dealPdmLatest).collect(toList());
    }

    private List<ECADIteration> getAllActiveMcadIterations() {
        ECADCatalog catalog = getActiveCatalog(SessionHelper.getCurrentUser().getOid());
        List<ECAD> mcads = ecadCatalogDomainService.queryECADByParentOid(catalog.getOid());
        List<String> mcadOids = mcads.stream().map(ECAD::getOid).collect(toList());

        return ecadIterationDomainService.queryLatestIterationBatch(mcadOids);
    }

    @Override
    public void checkinConfirm(CheckinConfirmParams params) {
        String account = SessionHelper.getCurrentUser().getAccount();
        for (CheckinConfirmParams.DDD oidAndPath : params.getOidsAndPaths()) {
            ECADIntegrationDTO ecadIntegrationDTO = null;
            ECADIteration ecadIteration = ecadIterationDomainService.findByOid(oidAndPath.getOid());

            if (ecadIteration.isCheckin()) {
                continue;
            }

            if (params.isForce()) {
                ecadIntegrationDTO = createEcadIntegrationDTO(ecadIteration);
                PdmECADIteration pdmECADIteration = pdmRemote.ecadCheckin(ecadIntegrationDTO);
                writeBackPdmECADData(pdmECADIteration, ecadIteration);
            }

//            ECADCatalog ecadCatalog = ecadIterationDomainService.queryCatalogByEcadIterationOid(ecadIteration.getOid());
//
//            if (!ecadCatalog.getContainerOid().equals(ecadIteration.getContainerOid())) {
//                continue;
//            }

            if (StringUtils.isNotBlank(ecadIteration.getLockOwnerAccount()) && !ecadIteration.getLockOwnerAccount().equals(account)) {
                continue;
            }

            if (ecadIteration.getPdmVersion() != null && !ecadIteration.getPdmVersion().equals(ecadIteration.getPdmLatestVersion())) {
                continue;
            }

            ecadIntegrationDTO = createEcadIntegrationDTO(ecadIteration);
            PdmECADIteration pdmECADIteration = pdmRemote.ecadCheckin(ecadIntegrationDTO);
            writeBackPdmECADData(pdmECADIteration, ecadIteration);
        }

    }

    private ECADIntegrationDTO createEcadIntegrationDTO(ECADIteration ecadIteration) {
        ECADIntegrationDTO ecadIntegrationDTO = new ECADIntegrationDTO();
        ecadIntegrationDTO.setProjectName(ecadIteration.getProjectName());
        ecadIntegrationDTO.setEcadNumber(ecadIteration.getSourceOid() == null ? "" : ecadIteration.getSourceOid());

        ecadIntegrationDTO.setEcadName(ecadIteration.getEcadName());
        ecadIntegrationDTO.setModelType(ecadIteration.getModelType());
        ecadIntegrationDTO.setProductPath(ecadIteration.getProductPath());
        ecadIntegrationDTO.setLocalPathOid(ecadIteration.getLocalPathOid());
        ecadIntegrationDTO.setVisiableOid(ecadIteration.getVisiableOid());
        ecadIntegrationDTO.setBomDataOid(ecadIteration.getBomDataOid());
        ecadIntegrationDTO.setNetList(ecadIteration.getNetList());
        ecadIntegrationDTO.setSchematicNumber(ecadIteration.getSchematicNumber());

        ecadIntegrationDTO.setEcadTrueNumber(ecadIteration.getEcadTrueNumber());
        ecadIntegrationDTO.setEcadModel(ecadIteration.getEcadModel());
        ecadIntegrationDTO.setProductInfoPathOid(ecadIteration.getProductInfoPathOid());
        ecadIntegrationDTO.setToolType(ecadIteration.getToolType());
        ecadIntegrationDTO.setToolVersion(ecadIteration.getToolVersion());
        ecadIntegrationDTO.setPackagePathOid(ecadIteration.getPackagePathOid());
        ecadIntegrationDTO.setSteelMeshList(ecadIteration.getSteelMeshList());
        return ecadIntegrationDTO;
    }

    @Override
    public void checkout(String oid) {
        ECADIteration ecadIteration = ecadIterationDomainService.findByOid(oid);
        if (StringUtils.isNotBlank(ecadIteration.getLockSourceOid())) {
            throw new JWIServiceException("该节点已经检出，无需再次检出");
        }

        ModelInfo modelInfo = buildModelInfo(ecadIteration.getSourceOid(), ecadIteration);

        JSONObject checkout = pdmRemote.checkout(modelInfo);

        ecadIteration.setLockNote(checkout.getString("lockNote"));
        ecadIteration.setLockedTime(checkout.getLong("lockedTime"));
        ecadIteration.setLockOwnerOid(checkout.getString("lockOwnerOid"));
        ecadIteration.setLockOwnerAccount(checkout.getString("lockOwnerAccount"));
        ecadIteration.setLockSourceOid(checkout.getString("oid"));
        ecadIterationDomainService.updateLockByOid(ecadIteration);
    }

    @Override
    public void cancelCheckOut(String oid) {
        ECADIteration ecadIteration = ecadIterationDomainService.findByOid(oid);
        if (StringUtils.isBlank(ecadIteration.getLockSourceOid())) {
            throw new JWIServiceException("该节点尚未检出，请先检出");
        }

        ModelInfo modelInfo = buildModelInfo(ecadIteration.getLockSourceOid(), ecadIteration);
        pdmRemote.cancelCheckOut(modelInfo);

        ecadIteration.setLockNote(null);
        ecadIteration.setLockedTime(null);
        ecadIteration.setLockOwnerOid(null);
        ecadIteration.setLockOwnerAccount(null);
        ecadIteration.setLockSourceOid(null);
        ecadIterationDomainService.updateLockByOid(ecadIteration);
    }

    private ModelInfo buildModelInfo(String oid, ECADIteration ecadIteration) {
        ModelInfo modelInfo = new ModelInfo();
        modelInfo.setOid(oid);
        modelInfo.setType(ecadIteration.getType());
        modelInfo.setModelDefinition(ecadIteration.getModelDefinition());
        return modelInfo;
    }

    @Override
    public List<ECADFileDTO> download(String iterationOid) {
        ECADIteration ecadIteration = ecadIterationDomainService.findByOid(iterationOid);

        ECADCatalog ecadCatalog = ecadIterationDomainService.queryCatalogByEcadIterationOid(ecadIteration.getOid());

        List<ECADFileDTO> fileDTOS = new ArrayList<>();

        ECADFileDTO primaryFile = new ECADFileDTO();
        primaryFile.setFileOid(ecadIteration.getLocalPathOid());
        primaryFile.setFileName(ecadIteration.getEcadName());
        primaryFile.setDirPath(Lists.newArrayList(ecadCatalog.getName()));
        fileDTOS.add(primaryFile);

        if (StringUtil.isNotBlank(ecadIteration.getPackagePathOid())) {
            ECADFileDTO packageFile = new ECADFileDTO();
            packageFile.setFileOid(ecadIteration.getPackagePathOid());
            packageFile.setFileName(ecadIteration.getPackagePathName());
            packageFile.setDirPath(Lists.newArrayList(ecadCatalog.getName()));
            fileDTOS.add(packageFile);
        }

        for (File file : ecadIteration.getNetList()) {
            ECADFileDTO netFile = new ECADFileDTO();
            netFile.setFileOid(file.getOid());
            netFile.setFileName(file.getName());
            netFile.setDirPath(Lists.newArrayList(ecadCatalog.getName(), ecadIteration.getName() + ECADConstant.ALLEGRO));
            fileDTOS.add(netFile);
        }
        return fileDTOS;
    }

    private ECAD createECAD(String userOid, String nodeName, ECADCatalog ecadCatalog) {
        ECAD newECAD = new ECAD();
        newECAD.setOid(OidGenerator.newOid());
        newECAD.setUserOid(userOid);
        newECAD.setName(nodeName);
        newECAD.setContainerOid(ecadCatalog.getContainerOid());
        newECAD.setSpaceContainerOid(ecadCatalog.getSpaceContainerOid());
        return ecadDomainService.create(newECAD);
    }

    private ECADIteration buildECADIteration(ECADIntegrationDTO integrationDTO, String userOid, long iterationVersion,
                                             ECADCatalog ecadCatalog, ECADIteration oldIteration) {
        ECADIteration ecadIteration = buildECADIteration(integrationDTO, userOid, iterationVersion, ecadCatalog);

        ecadIteration.setSpaceLifecycleCode(SpaceLifecycleEnum.UPDATE.getCode());
        ecadIteration.setSpaceLifecycleStatus(SpaceLifecycleEnum.UPDATE.getStatus());

        ecadIteration.setLifecycleOid(oldIteration.getLifecycleOid());
        ecadIteration.setLifecycleStatus(oldIteration.getLifecycleStatus());
        ecadIteration.setPdmVersion(oldIteration.getPdmVersion());
        ecadIteration.setPdmLatestVersion(oldIteration.getPdmLatestVersion());
        ecadIteration.setContainerOid(oldIteration.getContainerOid());
        ecadIteration.setContainerType(oldIteration.getContainerType());
        ecadIteration.setContainerModel(oldIteration.getContainerModel());

        ecadIteration.setPdmFileLastModified(oldIteration.getPdmFileLastModified());

        ecadIteration.setPnumber(oldIteration.getPnumber());
        ecadIteration.setNumber(oldIteration.getNumber());

        ecadIteration.setSourceOid(oldIteration.getSourceOid());
        ecadIteration.setSimpleIcon(oldIteration.getSimpleIcon());

        ecadIteration.setLockNote(oldIteration.getLockNote());
        ecadIteration.setLockOwnerAccount(oldIteration.getLockOwnerAccount());
        ecadIteration.setLockOwnerOid(oldIteration.getLockOwnerOid());
        ecadIteration.setLockedTime(oldIteration.getLockedTime());
        ecadIteration.setLockSourceOid(oldIteration.getLockSourceOid());
        return ecadIteration;
    }

    private ECADIteration buildECADIteration(ECADIntegrationDTO integrationDTO, String userOid, long iterationVersion, ECADCatalog ecadCatalog) {
        ECADIteration ecadIteration = new ECADIteration();
        ecadIteration.setOid(OidGenerator.newOid());
        ecadIteration.setName(integrationDTO.getProjectName());
//        ecadIteration.setState(params.getState());
        ecadIteration.setPnumber(integrationDTO.getEcadNumber());
        ecadIteration.setNumber(integrationDTO.getEcadNumber());
        ecadIteration.setProjectName(integrationDTO.getProjectName());
        ecadIteration.setEcadNumber(ecadIteration.getOid());
        ecadIteration.setEcadName(integrationDTO.getEcadName());
        ecadIteration.setModelType(integrationDTO.getModelType());
        ecadIteration.setProductPath(integrationDTO.getProductPath());
        ecadIteration.setLocalPathOid(integrationDTO.getLocalPathOid());
        ecadIteration.setVisiableOid(integrationDTO.getVisiableOid());
        ecadIteration.setBomDataOid(integrationDTO.getBomDataOid());
        ecadIteration.setNetList(integrationDTO.getNetList());
        ecadIteration.setSchematicNumber(integrationDTO.getSchematicNumber());
        ecadIteration.setEcadTrueNumber(integrationDTO.getEcadTrueNumber());
        ecadIteration.setEcadModel(integrationDTO.getEcadModel());
        ecadIteration.setProductInfoPathOid(integrationDTO.getProductInfoPathOid());
        ecadIteration.setToolType(integrationDTO.getToolType());
        ecadIteration.setToolVersion(integrationDTO.getToolVersion());
        ecadIteration.setPackagePathOid(integrationDTO.getPackagePathOid());
        ecadIteration.setSteelMeshList(integrationDTO.getSteelMeshList());

//        ecadIteration.setOpacity(params.getOpacity());
//        ecadIteration.setRgb(params.getRgb());
//        ecadIteration.setProjName(params.getProjName());
//        ecadIteration.setPdmVersion(params.getPdmVersion());
//        ecadIteration.setPdmLatestVersion(params.getPdmVersion());
//        ecadIteration.setPdmType(params.getType());
//        ecadIteration.setParams(params.getParams());
//        ecadIteration.setShapeInfo(params.getShapeInfo());
//        ecadIteration.setPmiInfo(params.getPmiInfo());
//        ecadIteration.setAnnotationInfo(params.getAnnotationInfo());

        ecadIteration.setLifecycleOid("");
        ecadIteration.setLifecycleStatus("");

        ecadIteration.setUserOid(userOid);
        ecadIteration.setVersion("V.");
        ecadIteration.setLatest(true);
        ecadIteration.setIteratedVersion(iterationVersion);
        ecadIteration.setDisplayVersion("V." + iterationVersion);

        ecadIteration.setContainerOid(ecadCatalog.getContainerOid());
        ecadIteration.setContainerType(ecadCatalog.getContainerType());
        ecadIteration.setContainerModel(ecadCatalog.getContainerModel());
        ecadIteration.setSpaceContainerOid(ecadCatalog.getSpaceContainerOid());

        ecadIteration.setCheckin(false);

        String productPath = integrationDTO.getProductPath();
        String[] split = trim(productPath).split("<jwsplit>");
        ecadIteration.setPdmCatalogFullPaths(Arrays.asList(split));

        ecadIteration.setSpaceLifecycleCode(SpaceLifecycleEnum.ADD.getCode());
        ecadIteration.setSpaceLifecycleStatus(SpaceLifecycleEnum.ADD.getStatus());
//        ecadIteration.setSubType(params.getSubType());
        return ecadIteration;
    }


    @Override
    public List<ECADDTO> histories(String oid) {
        List<ECADIteration> ecadIterations = ecadIterationDomainService.histories(oid);
        return BeanUtil.cloneList(ecadIterations, ECADDTO.class);
    }

    private ECADIteration buildECADIteration(ECADIteration oldIteration, PdmECADIteration pdmECADIteration) {
        ECADIteration ecadIteration = BeanUtil.clone(oldIteration, ECADIteration.class);

        ecadIteration.setOid(OidGenerator.newOid());
        ecadIteration.setOpacity(pdmECADIteration.getOpacity());
        ecadIteration.setRgb(pdmECADIteration.getRgb());
        ecadIteration.setPdmVersion(pdmECADIteration.getDisplayVersion());
        ecadIteration.setPdmLatestVersion(pdmECADIteration.getDisplayVersion());

        ecadIteration.setLifecycleOid(pdmECADIteration.getLifecycleOid());
        ecadIteration.setLifecycleStatus(pdmECADIteration.getLifecycleStatus());

        ecadIteration.setLatest(true);
        ecadIteration.setIteratedVersion(oldIteration.getIteratedVersion() + 1);
        ecadIteration.setDisplayVersion("V." + ecadIteration.getIteratedVersion());

        ecadIteration.setCheckin(true);
        return ecadIteration;
    }

    @Override
    public Object initOrUpdateLibrary(String path) {
        return pdmRemote.initOrUpdateLibrary(path);
    }

    @Override
    public Object searchCatalogTree() {
        return pdmRemote.searchCatalogTree();
    }

    @Override
    public Object searchSchematic() {
        return pdmRemote.searchSchematic();
    }

    @Override
    public Object syncToolData(Object object) {
        return pdmRemote.syncToolData(object);
    }
}
