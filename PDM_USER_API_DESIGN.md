# PDM用户服务接口设计文档

## 概述

本文档描述了PDM端需要实现的用户服务接口，用于支持Space系统查询用户信息和token认证。

## 基础配置

需要在application.yml中配置PDM用户服务地址：

```yaml
pdm:
  user:
    service:
      gateway:
        url: http://your-pdm-user-service-url
```

## 接口列表

### 1. 根据用户账号查询用户信息

**接口地址**: `GET /user/getUserByAccount`

**请求参数**:
- `account` (String, 必填): 用户账号

**响应格式**:
```json
{
  "code": 0,
  "msg": "success",
  "result": {
    "oid": "user123456",
    "account": "zhangsan",
    "name": "张三",
    "email": "<EMAIL>",
    "tenantOid": "tenant001",
    "tenantName": "测试租户",
    "departmentOid": "dept001",
    "departmentName": "研发部",
    "roles": "ADMIN,USER",
    "status": "ACTIVE",
    "createTime": "2024-01-01 10:00:00",
    "lastLoginTime": "2024-01-15 09:30:00"
  }
}
```

### 2. 根据用户OID查询用户信息

**接口地址**: `GET /user/getUserByOid`

**请求参数**:
- `userOid` (String, 必填): 用户OID

**响应格式**: 同上

### 3. 获取用户认证信息

**接口地址**: `POST /user/getUserAuthInfo`

**请求体**:
```json
{
  "account": "zhangsan",
  "userOid": "user123456",
  "tenantOid": "tenant001",
  "includeAuthInfo": true,
  "includeRoles": true
}
```

**响应格式**:
```json
{
  "code": 0,
  "msg": "success",
  "result": {
    "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refreshToken": "refresh_token_here",
    "tokenType": "Bearer",
    "expiresIn": 3600,
    "scope": "read write",
    "userInfo": {
      "oid": "user123456",
      "account": "zhangsan",
      "name": "张三",
      "email": "<EMAIL>",
      "tenantOid": "tenant001",
      "tenantName": "测试租户",
      "departmentOid": "dept001",
      "departmentName": "研发部",
      "roles": "ADMIN,USER",
      "status": "ACTIVE",
      "createTime": "2024-01-01 10:00:00",
      "lastLoginTime": "2024-01-15 09:30:00"
    }
  }
}
```

### 4. 验证token有效性

**接口地址**: `GET /auth/validateToken`

**请求参数**:
- `accessToken` (String, 必填): 访问令牌

**响应格式**:
```json
{
  "code": 0,
  "msg": "success",
  "result": true
}
```

### 5. 刷新用户token

**接口地址**: `POST /auth/refreshToken`

**请求参数**:
- `refreshToken` (String, 必填): 刷新令牌

**响应格式**:
```json
{
  "code": 0,
  "msg": "success",
  "result": {
    "accessToken": "new_access_token_here",
    "refreshToken": "new_refresh_token_here",
    "tokenType": "Bearer",
    "expiresIn": 3600,
    "scope": "read write",
    "userInfo": {
      // 用户信息同上
    }
  }
}
```

### 6. 用户登录

**接口地址**: `POST /auth/login`

**请求参数**:
- `account` (String, 必填): 用户账号
- `password` (String, 必填): 用户密码

**响应格式**: 同刷新token接口

### 7. 用户登出

**接口地址**: `POST /auth/logout`

**请求参数**:
- `accessToken` (String, 必填): 访问令牌

**响应格式**:
```json
{
  "code": 0,
  "msg": "success",
  "result": null
}
```

## 错误响应格式

```json
{
  "code": -1,
  "msg": "错误信息",
  "result": null
}
```

## 常见错误码

- `1001`: 用户不存在
- `1002`: 用户已禁用
- `1003`: token无效或已过期
- `1004`: 刷新token无效
- `1005`: 密码错误
- `1006`: 账号被锁定

## 数据库表设计建议

### 用户表 (users)

```sql
CREATE TABLE users (
    oid VARCHAR(64) PRIMARY KEY,
    account VARCHAR(100) UNIQUE NOT NULL,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(200),
    password_hash VARCHAR(255) NOT NULL,
    tenant_oid VARCHAR(64),
    tenant_name VARCHAR(100),
    department_oid VARCHAR(64),
    department_name VARCHAR(100),
    roles TEXT,
    status VARCHAR(20) DEFAULT 'ACTIVE',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    last_login_time TIMESTAMP
);
```

### 用户token表 (user_tokens)

```sql
CREATE TABLE user_tokens (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_oid VARCHAR(64) NOT NULL,
    access_token TEXT NOT NULL,
    refresh_token TEXT NOT NULL,
    token_type VARCHAR(20) DEFAULT 'Bearer',
    expires_in BIGINT NOT NULL,
    scope VARCHAR(200),
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expire_time TIMESTAMP NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    INDEX idx_user_oid (user_oid),
    INDEX idx_access_token (access_token(100)),
    INDEX idx_refresh_token (refresh_token(100))
);
```

## 安全建议

1. 所有接口都应该进行身份验证
2. token应该设置合理的过期时间
3. 敏感信息（如密码）不应该在响应中返回
4. 使用HTTPS传输
5. 实现请求频率限制
6. 记录审计日志
