<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>cn.jwis.platform.space</groupId>
        <artifactId>space-ecad-server</artifactId>
        <version>3.4.0_yhht</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>ecadspace-launcher</artifactId>
    <version>3.4.0_yhht</version>

    <properties>
        <!-- properties 必须要有一个namespace,否则开发云运行会失败 -->
        <namespace></namespace>
        <maven.deploy.skip>true</maven.deploy.skip>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>1.8</java.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>cn.jwis.platform.space</groupId>
            <artifactId>ecadspace-web</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.jwis.platform.space</groupId>
            <artifactId>ecadspace-repo-neo4j</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.jwis.platform.space</groupId>
            <artifactId>ecadspace-remote-service</artifactId>
        </dependency>
        <!--        配置中心-->
        <dependency>
            <groupId>cn.jwis.framework</groupId>
            <artifactId>config-center-sdk</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.1</version>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                    <encoding>UTF-8</encoding>
                    <!-- 此处配置打包时必须排除本地的配置文件，防止配置中心的配置信息失效-->
                    <excludes>
                        <exclude>**/application*.properties</exclude>
                    </excludes>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>2.5.4</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>

        <resources>
            <resource>
                <directory>src/lib</directory>
                <targetPath>/lib</targetPath>
                <includes>
                    <include>**/*.jar</include>
                </includes>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <includes>
<!--                    <include>*.properties</include>-->
                    <include>**/i18n/*.properties</include>
                    <include>**/*.xml</include>
                    <include>**/*.yaml</include>
                </includes>
                <filtering>false</filtering>
            </resource>
        </resources>
    </build>
</project>