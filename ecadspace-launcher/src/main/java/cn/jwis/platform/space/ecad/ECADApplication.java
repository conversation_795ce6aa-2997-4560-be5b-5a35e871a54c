package cn.jwis.platform.space.ecad;

import cn.jwis.framework.configration.ConfigCenterHelper;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.scheduling.annotation.EnableAsync;

import java.util.HashMap;
import java.util.Map;

@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class})
@EnableAspectJAutoProxy
@ComponentScan(basePackages = {"cn.jwis"})
@EnableFeignClients
@EnableAsync
public class ECADApplication {

    public static final String SERVICE_NAME = "space-ecad-server";

    public static void main(String[] args) {
        SpringApplication springApplication = new SpringApplication(ECADApplication.class);

        Map<String, Object> defaultMap = new HashMap<>();
        ConfigCenterHelper.getConfig(defaultMap, SERVICE_NAME);
        springApplication.setDefaultProperties(defaultMap);

        springApplication.run(args);
    }

}
