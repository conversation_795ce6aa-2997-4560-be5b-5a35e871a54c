server.port=8109
server.servlet.context-path=/space-ecad-server

test.datasource.type=neo4j

#\u8BBE\u7F6E\u56FD\u9645\u5316\u914D\u7F6E\u6587\u4EF6\u5B58\u653E\u5728classpath:/i18n\u76EE\u5F55\u4E0B
spring.messages.basename=i18n/message,i18n/error
#\u8BBE\u5B9Amessage bundles\u7F16\u7801\u65B9\u5F0F\uFF0C\u9ED8\u8BA4\u4E3AUTF-8
spring.messages.encoding=UTF-8

#neo4j\u6570\u636E\u5E93\u914D\u7F6E
spring.data.neo4j.uri=bolt://**************:7687
spring.data.neo4j.username=neo4j
spring.data.neo4j.password=neo
spring.data.neo4j.database=neo4j
tenant.neo4j.prefix=neo4j
database.storage.type = neo4j
spring.data.neo4j.default.database=neo4j

##Config Logging Export
logging.level.org.apache.ibatis=debug
logging.file=logs/log.log
log4j.logger.org.apache.ibatis=INFO
log4j.logger.java.sql=INFO 
##\u4FEE\u590DSwagger Bug
logging.level.io.swagger.models.parameters.AbstractSerializableParameter=ERROR

#\u914D\u7F6ERedis\u4FE1\u606F
spring.redis.host=************
spring.redis.port=6379
spring.redis.database=3
spring.redis.password=redis
spring.redis.pool.max-idle=8
spring.redis.pool.max-wait=-1
spring.redis.pool.min-idle=0
spring.redis.pool.max-active=8
spring.redis.connect.cluster=false
saas.redis.index.start=100
saas.redis.saas.storage=11

swagger.enabled=true
swagger.base-package=cn.jwis
swagger.title=ecadspace service APIs
swagger.version=1.0.0
swagger.description=Description

#Account\u670D\u52A1\u8BBF\u95EE\u5730\u5740
account.service.url = http://gateway.dev.jwis.cn/jic-account
#account.service.url = http://localhost:16301/jic-account
file.service.url = http://gateway.dev.jwis.cn/file
logging.level.cn.jwis.app.infrastructure.feign=debug

pdm.cad.service.gateway.url = http://gateway.dev.jwis.cn/cad-micro
pdm.container.service.gateway.url = http://gateway.dev.jwis.cn/container
pdm.foundation.service.gateway.url = http://gateway.dev.jwis.cn/foundation-micro
search-engine.service.gateway.url = http://gateway.dev.jwis.cn/search-engine-micro
file.service.gateway.url = http://gateway.dev.jwis.cn/file-micro