package cn.jwis.platform.space.ecad.web;

import cn.jwis.framework.base.response.PageResult;
import cn.jwis.framework.base.response.Result;
import cn.jwis.platform.space.ecad.remote.ECADIntegrationDTO;
import cn.jwis.platform.space.ecad.remote.ECADParam;
import cn.jwis.platform.space.ecad.service.dto.*;
import cn.jwis.platform.space.ecad.service.interf.ECADCatalogService;
import cn.jwis.platform.space.ecad.service.interf.ECADService;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/ecads")
@RequiredArgsConstructor
@Slf4j
public class ECADController {

    private final ECADService ecadService;

    @ApiOperation("保存、检入")
    @PostMapping("/saveAndCheckin")
    public Result<Void> saveAndCheckin(@RequestBody ECADIntegrationDTO ecadIntegrationDTO) {

        log.info("进入 saveAndCheckin 方法, 请求参数: {}", JSON.toJSONString(ecadIntegrationDTO));
        Result<Void> result = Result.success(ecadService.saveAndCheckin(ecadIntegrationDTO));
        log.info("saveAndCheckin 执行完成, 返回: {}", result);
        return result;
    }

    @ApiOperation("历史记录")
    @GetMapping("/histories")
    public Result<List<ECADDTO>> histories(@ApiParam("节点oid") @RequestParam String oid) {
        log.info("进入 histories 方法, 节点oid: {}", oid);
        List<ECADDTO> list = ecadService.histories(oid);
        log.info("histories 返回记录数: {}", list != null ? list.size() : 0);
        return Result.success(list);
    }

    @ApiOperation("查询指定文件夹下的节点列表")
    @GetMapping("/getList")
    public Result<PageResult<ECADDTO>> byECADCatalogOid(
            @ApiParam(value = "page", defaultValue = "1") @RequestParam(required = false, defaultValue = "1") Integer page,
            @ApiParam(value = "size", defaultValue = "20") @RequestParam(required = false, defaultValue = "20") Integer size,
            @ApiParam("文件夹oid") @RequestParam String ecadCatalogOid,
            @ApiParam("名称, 模糊查询") @RequestParam(required = false) String name) {
        log.info("进入 getList 方法, page: {}, size: {}, ecadCatalogOid: {}, name: {}", page, size, ecadCatalogOid, name);
        PageResult<ECADDTO> result = ecadService.getList(page, size, ecadCatalogOid, name);
        log.info("getList 查询完成, 返回记录数: {}", result != null ? result.getCount() : 0);
        return Result.success(result);
    }

    @ApiOperation("单个节点的下载")
    @GetMapping("/download")
    public Result<List<ECADFileDTO>> download(@ApiParam(value = "节点oid", required = true) @RequestParam String oid) {
        log.info("进入 download 方法, 节点oid: {}", oid);
        List<ECADFileDTO> files = ecadService.download(oid);
        log.info("download 执行完成, 文件数: {}", files != null ? files.size() : 0);
        return Result.success(files);
    }

    @ApiOperation("检入数据预览")
    @PostMapping("/checkin/preview")
    public Result<List<ECADDTO>> checkinPreview(@RequestBody List<String> oids) {
        log.info("进入 checkinPreview 方法, oids: {}", oids);
        List<ECADDTO> previewList = ecadService.checkinPreview(oids);
        log.info("checkinPreview 执行完成, 返回记录数: {}", previewList != null ? previewList.size() : 0);
        return Result.success(previewList);
    }

    @ApiOperation("检入确定")
    @PostMapping("/checkin/confirm")
    public Result<Void> checkinConfirm(@RequestBody CheckinConfirmParams params) {
        log.info("进入 checkinConfirm 方法, 请求参数: {}", params);
        ecadService.checkinConfirm(params);
        log.info("checkinConfirm 执行完成");
        return Result.success();
    }

    @ApiOperation("详情")
    @GetMapping("/detail")
    public Result<List<ECADDTODetail>> detail(@ApiParam(value = "节点oid", required = true) @RequestParam String oid) {
        log.info("进入 detail 方法, 节点oid: {}", oid);
        List<ECADDTODetail> details = ecadService.queryDetail(oid);
        log.info("detail 执行完成, 返回记录数: {}", details != null ? details.size() : 0);
        return Result.success(details);
    }

    @ApiOperation("检出")
    @GetMapping("/checkout")
    public Result<ECADAssemblyNode> checkout(@ApiParam(value = "节点oid", required = true) @RequestParam String oid) {
        log.info("进入 checkout 方法, 节点oid: {}", oid);
        ecadService.checkout(oid);
        log.info("checkout 执行完成");
        return Result.success();
    }

    @ApiOperation("取消检出")
    @GetMapping("/cancelCheckOut")
    public Result<ECADAssemblyNode> cancelCheckOut(@ApiParam(value = "节点oid", required = true) @RequestParam String oid) {
        log.info("进入 cancelCheckOut 方法, 节点oid: {}", oid);
        ecadService.cancelCheckOut(oid);
        log.info("cancelCheckOut 执行完成");
        return Result.success();
    }

    @ApiOperation("同步状态")
    @PostMapping("/syncStatus")
    public Result<Void> syncStatus(@RequestBody List<String> oids) {
        log.info("进入 syncStatus 方法, oids: {}", oids);
        ecadService.syncStatus(oids);
        log.info("syncStatus 执行完成, 处理oid数量: {}", oids != null ? oids.size() : 0);
        return Result.success();
    }
}
