package cn.jwis.platform.space.ecad.web;

import cn.jwis.framework.base.response.Result;
import cn.jwis.platform.space.ecad.remote.PdmUserAuthInfo;
import cn.jwis.platform.space.ecad.remote.PdmUserInfo;
import cn.jwis.platform.space.ecad.remote.PdmUserQueryParams;
import cn.jwis.platform.space.ecad.service.interf.UserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * 用户控制器
 */
@Api(tags = "用户管理")
@RestController
@RequestMapping("/user")
@RequiredArgsConstructor
public class UserController {
    
    private final UserService userService;
    
    @ApiOperation("根据账号查询用户信息")
    @GetMapping("/getUserByAccount")
    public Result<PdmUserInfo> getUserByAccount(
            @ApiParam("用户账号") @RequestParam String account) {
        PdmUserInfo userInfo = userService.getUserByAccount(account);
        return Result.success(userInfo);
    }
    
    @ApiOperation("根据OID查询用户信息")
    @GetMapping("/getUserByOid")
    public Result<PdmUserInfo> getUserByOid(
            @ApiParam("用户OID") @RequestParam String userOid) {
        PdmUserInfo userInfo = userService.getUserByOid(userOid);
        return Result.success(userInfo);
    }
    
    @ApiOperation("获取用户认证信息")
    @PostMapping("/getUserAuthInfo")
    public Result<PdmUserAuthInfo> getUserAuthInfo(
            @ApiParam("查询参数") @RequestBody PdmUserQueryParams params) {
        PdmUserAuthInfo authInfo = userService.getUserAuthInfo(params);
        return Result.success(authInfo);
    }
    
    @ApiOperation("验证token有效性")
    @GetMapping("/validateToken")
    public Result<Boolean> validateToken(
            @ApiParam("访问令牌") @RequestParam String accessToken) {
        Boolean isValid = userService.validateToken(accessToken);
        return Result.success(isValid);
    }
    
    @ApiOperation("刷新用户token")
    @PostMapping("/refreshToken")
    public Result<PdmUserAuthInfo> refreshToken(
            @ApiParam("刷新令牌") @RequestParam String refreshToken) {
        PdmUserAuthInfo authInfo = userService.refreshToken(refreshToken);
        return Result.success(authInfo);
    }
    
    @ApiOperation("获取当前用户信息")
    @GetMapping("/getCurrentUser")
    public Result<PdmUserInfo> getCurrentUser() {
        PdmUserInfo userInfo = userService.getCurrentUser();
        return Result.success(userInfo);
    }
    
    @ApiOperation("获取当前用户访问令牌")
    @GetMapping("/getCurrentUserAccessToken")
    public Result<String> getCurrentUserAccessToken() {
        String accessToken = userService.getCurrentUserAccessToken();
        return Result.success(accessToken);
    }
}
