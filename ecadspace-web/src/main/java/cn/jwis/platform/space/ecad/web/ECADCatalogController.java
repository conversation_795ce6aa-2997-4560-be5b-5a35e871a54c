package cn.jwis.platform.space.ecad.web;

import cn.jwis.framework.base.response.Result;
import cn.jwis.platform.space.ecad.entity.ECADCatalog;
import cn.jwis.platform.space.ecad.remote.PdmCatalogNode;
import cn.jwis.platform.space.ecad.service.dto.*;
import cn.jwis.platform.space.ecad.service.interf.ECADCatalogService;
import com.google.common.collect.Lists;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@RestController
@RequestMapping("/ecadCatalogs")
@RequiredArgsConstructor
public class ECADCatalogController {

    private final ECADCatalogService ecadCatalogService;

    @ApiOperation("新建工作区")
    @PostMapping("/create")
    public Result<SyncResDTO> create(@RequestBody ECADCatalogCreateParams params) {
        ecadCatalogService.create(params);

        List<List<String>> list = new ArrayList<>();
        list.add(Lists.newArrayList(params.getName()));
        return Result.success(new SyncResDTO(Collections.emptyList(), list));
    }

    @ApiOperation("删除工作区")
    @GetMapping("/delete")
    public Result<Void> delete(@ApiParam("文件夹oid") @RequestParam String oid) {
        ecadCatalogService.delete(oid);
        return Result.success();
    }

    @ApiOperation("清空工作区")
    @GetMapping("/clear")
    public Result<Void> clear(@ApiParam("文件夹oid") @RequestParam String oid) {
        ecadCatalogService.clear(oid);
        return Result.success();
    }

    @ApiOperation("激活工作区")
    @GetMapping("/activate")
    public Result<Void> activate(@ApiParam("文件夹oid") @RequestParam String oid) {
        ecadCatalogService.activate(oid);
        return Result.success();
    }

    @ApiOperation("查询工作区")
    @GetMapping("/workspaces")
    public Result<List<ECADCatalogNode>> tree() {
        List<ECADCatalogNode> nodes = ecadCatalogService.tree();
        return Result.success(nodes);
    }

    @ApiOperation("查询已激活工作区对应的pdm菜单树")
    @GetMapping("/pdmTree")
    public Result<List<PdmCatalogNode>> pdmTree() {
        List<PdmCatalogNode> nodes = ecadCatalogService.pdmTree();
        return Result.success(nodes);
    }

    @ApiOperation("查询已激活工作区")
    @GetMapping("/activeWorkspace")
    public Result<ECADCatalog> activeWorkspace() {
        return Result.success(ecadCatalogService.activeWorkspace());
    }

}
