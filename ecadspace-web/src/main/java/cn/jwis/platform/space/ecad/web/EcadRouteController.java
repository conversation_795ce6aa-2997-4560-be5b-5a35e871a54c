package cn.jwis.platform.space.ecad.web;

import cn.jwis.framework.base.exception.JWIException;
import cn.jwis.framework.base.response.Result;
import cn.jwis.platform.space.ecad.service.interf.ECADService;
import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/ecad")
@RequiredArgsConstructor
public class EcadRouteController {

    private final ECADService ecadService;

    @ApiOperation("初始化或者更新资源库")
    @GetMapping("/integration/initOrUpdateLibrary")
    public Result<Object> initOrUpdateLibrary(@RequestParam("path") String path) throws JWIException {
        return Result.success(ecadService.initOrUpdateLibrary(path));
    }

    @ApiOperation("获取目录结构树")
    @GetMapping("/integration/searchCatalogTree")
    public Result searchCatalogTree() throws JWIException {
        return Result.success(ecadService.searchCatalogTree());
    }

    @ApiOperation("查询PDM系统中所有原理图的接口")
    @GetMapping("/integration/searchSchematic")
    public Result<List<Object>> searchSchematic() throws JWIException {
        return Result.success(ecadService.searchSchematic());
    }

    @ApiOperation("syncToolData")
    @PostMapping("/integration/eda/syncToolData")
    public Result<Object> syncToolData(@RequestBody Object object) throws JWIException {
        return Result.success(ecadService.syncToolData(object));
    }
}
