package cn.jwis.platform.space.ecad.web;

import cn.jwis.framework.base.response.Result;
import cn.jwis.platform.space.ecad.remote.ECADIntegrationDTO;
import cn.jwis.platform.space.ecad.service.dto.EDMFileDTO;
import cn.jwis.platform.space.ecad.service.dto.EDMIntegrationMeta;
import cn.jwis.platform.space.ecad.service.dto.SyncParams;
import cn.jwis.platform.space.ecad.service.dto.SyncResDTO;
import cn.jwis.platform.space.ecad.service.interf.ECADService;
import cn.jwis.platform.space.ecad.service.interf.IntegrationService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.openxml4j.opc.internal.FileHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@RestController
@RequestMapping("/integration")
@RequiredArgsConstructor
@Slf4j
public class IntegrationController {

    private final IntegrationService integrationService;

    private final ECADService ecadService;


    @ApiOperation("同步")
    @PostMapping("/sync")
    public Result<SyncResDTO> sync(@RequestBody SyncParams params) {
        return Result.success(integrationService.sync(params));
    }

    @ApiOperation("EDM文件压缩包接入并检入space")
    @PostMapping(value = "/edmIntegrationCheckin", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public JSONObject edmIntegrationCheckin(
            @RequestParam("meta") String metaJson,
            @RequestParam("file") MultipartFile zipFile) {
        log.info("进入 edmIntegrationCheckin 方法, meta: {}", metaJson);
        log.info("进入 edmIntegrationCheckin 方法, zipFileName: {}", zipFile.getOriginalFilename());

        return integrationService.edmIntegrationCheckin(metaJson, zipFile);
    }

}
