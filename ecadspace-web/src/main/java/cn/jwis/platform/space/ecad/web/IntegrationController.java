package cn.jwis.platform.space.ecad.web;

import cn.jwis.framework.base.response.Result;
import cn.jwis.platform.space.ecad.remote.ECADIntegrationDTO;
import cn.jwis.platform.space.ecad.service.dto.EDMFileDTO;
import cn.jwis.platform.space.ecad.service.dto.EDMIntegrationMeta;
import cn.jwis.platform.space.ecad.service.dto.SyncParams;
import cn.jwis.platform.space.ecad.service.dto.SyncResDTO;
import cn.jwis.platform.space.ecad.service.interf.ECADService;
import cn.jwis.platform.space.ecad.service.interf.IntegrationService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@RestController
@RequestMapping("/integration")
@RequiredArgsConstructor
@Slf4j
public class IntegrationController {

    private final IntegrationService integrationService;

    private final ECADService ecadService;

    @ApiOperation("同步")
    @PostMapping("/sync")
    public Result<SyncResDTO> sync(@RequestBody SyncParams params) {
        return Result.success(integrationService.sync(params));
    }

    @ApiOperation("EDM文件压缩包接入并检入space")
    @PostMapping(value = "/edmIntegrationCheckin", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public JSONObject edmIntegrationCheckin(
            @RequestParam("meta") String metaJson,
            @RequestParam("file") MultipartFile zipFile) {
        log.info("进入 edmIntegrationCheckin 方法, meta: {}", metaJson);
        log.info("进入 edmIntegrationCheckin 方法, zipFileName: {}", zipFile.getOriginalFilename());

        // 手动转换成对象
        ECADIntegrationDTO meta = JSON.parseObject(metaJson, ECADIntegrationDTO.class);
        log.info("meta:{}",JSON.toJSONString(meta));

        /*// 1. 解压文件
        List<File> extractedFiles = fileUnzipService.unzip(zipFile);

        // 2. 上传到 Minio 获取 fileOid
        List<EDMFileDTO> fileDTOs = minioUploadService.uploadFiles(extractedFiles);

        // 3. 组装 EDMIntegrationDTO
        EDMIntegrationDTO edmDto = EDMIntegrationDTO.builder()
                .projectName(meta.getProjectName())
                .ecadNumber(meta.getEcadNumber())
                .ecadName(meta.getEcadName())
                .files(fileDTOs)
                .build();

        // 4. 转换为 ECADIntegrationDTO 并调用 saveAndCheckin
        ECADIntegrationDTO ecadDto = edmFileMapper.mapToECADIntegrationDTO(edmDto);*/
        ECADIntegrationDTO ecadDto = new ECADIntegrationDTO();
        return ecadService.saveAndCheckin(ecadDto);
    }

}
