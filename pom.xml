<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <artifactId>space-ecad-server</artifactId>
    <packaging>pom</packaging>
    <version>3.4.0_yhht</version>
    <description>space-ecad-server</description>
    <modules>
        <module>ecadspace-helper</module>
        <module>ecadspace-web</module>
        <module>ecadspace-launcher</module>
        <module>ecadspace-remote</module>
        <module>ecadspace-remote-service</module>
    </modules>

    <parent>
        <groupId>cn.jwis.platform.space</groupId>
        <artifactId>jwi-platform-space-parent</artifactId>
        <version>3.4.0</version>
    </parent>

    <properties>
        <!-- properties 必须要有一个namespace,否则开发云运行会失败 -->
        <namespace></namespace>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>1.8</java.version>
        <jwi.platform.space.sdk.ecadspace.version>3.4.0_yhht</jwi.platform.space.sdk.ecadspace.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>cn.jwis.platform.space</groupId>
                <artifactId>ecadspace-helper</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.jwis.platform.space</groupId>
                <artifactId>ecadspace-web</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.jwis.platform.space</groupId>
                <artifactId>ecadspace-remote</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.jwis.platform.space</groupId>
                <artifactId>ecadspace-remote-service</artifactId>
                <version>${project.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>cn.jwis.framework</groupId>
            <artifactId>jwis-base-service</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.1</version>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                    <encoding>UTF-8</encoding>
                    <!--编译时接口中的函数的参数会以明文的方式存在class文件中，Repo中就可以不使用Param注解-->
                    <compilerArgs>
                        <arg>-parameters</arg>
                    </compilerArgs>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>versions-maven-plugin</artifactId>
                <version>2.8.1</version>
            </plugin>
        </plugins>
    </build>

    <distributionManagement>
        <repository>
            <id>maven-releases</id>
            <name>Nexus Release Repository</name>
            <url>http://jwi-in-nexus.jwis.cn/repository/maven-releases/</url>
        </repository>
    </distributionManagement>

</project>
